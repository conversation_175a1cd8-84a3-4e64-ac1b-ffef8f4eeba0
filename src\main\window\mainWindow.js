const { BrowserWindow, screen } = require('electron')
const { join } = require('path')

const isDev = process.env.NODE_ENV === 'development'

/**
 * 主窗口管理类
 */
class MainWindowManager {
  constructor(appManager) {
    this.appManager = appManager
    this.window = null
  }

  /**
   * 创建主窗口
   */
  createWindow() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show()
      return this.window
    }

    console.log('Creating main window...')
    
    // 获取屏幕信息和缩放比例
    let screenWidth = 1920, screenHeight = 1080
    try {
      if (screen && screen.getPrimaryDisplay) {
        const display = screen.getPrimaryDisplay()
        screenWidth = display.workAreaSize.width
        screenHeight = display.workAreaSize.height
      }
    } catch (error) {
      console.warn('Failed to get screen info, using defaults:', error.message)
    }
    console.log(`屏幕工作区尺寸: ${screenWidth}x${screenHeight}`)

    // 主窗口尺寸
    let scaleFactor = 1.0
    try {
      if (screen && screen.getPrimaryDisplay) {
        const primaryDisplay = screen.getPrimaryDisplay()
        scaleFactor = primaryDisplay.scaleFactor
      }
    } catch (error) {
      console.warn('Failed to get display scale factor, using default 1.0')
    }
    // 只支持标准缩放级别（100%, 125%, 150%, 175%），其他都按100%处理
    let windowWidth = 1200
    let windowHeight = 800

    // 根据屏幕尺寸调整窗口大小
    if (screenWidth < 1400 || screenHeight < 900) {
      windowWidth = Math.min(1000, screenWidth - 100)
      windowHeight = Math.min(700, screenHeight - 100)
    } else if (screenWidth < 1200 || screenHeight < 700) {
      windowWidth = 800
      windowHeight = 500
    }

    // 根据缩放比例调整窗口位置
    const reverseScale = 1.0 / this.appManager.displayScale
    console.log(`窗口位置计算：反向缩放系数 ${reverseScale}`)

    // 计算居中位置，考虑缩放影响
    const centerX = Math.round((screenWidth - windowWidth) / 2) - 100
    const centerY = Math.round((screenHeight - windowHeight) / 2)

    console.log(`主窗口初始位置: x=${centerX}, y=${centerY}`)

    this.window = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      // 设置窗口初始位置（屏幕中央）
      x: centerX,
      y: centerY,
      show: true,
      autoHideMenuBar: true,
      icon: isDev
        ? join(process.cwd(), 'public/assets/logo.ico')
        : join(process.resourcesPath || process.cwd(), 'assets/logo.ico'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: join(__dirname, '../../preload/preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        // 启用 webgl 以支持更好的渲染性能
        webgl: true,
        // 启用硬件加速
        hardwareAcceleration: true,
        // 允许访问本地文件（用于加载本地资源）
        allowFileAccessFromFileURLs: false,
        allowUniversalAccessFromFileURLs: false,
        // 禁用 node integration 但允许 preload 脚本
        sandbox: false,
        // 启用上下文隔离以提高安全性
        contextIsolation: true,
        // 禁用 web security 以允许跨域请求（仅在开发环境）
        webSecurity: !isDev,
        // 允许运行不安全内容（仅在开发环境）
        allowRunningInsecureContent: isDev,
        // 启用实验性功能
        experimentalFeatures: true,
        // 启用 SharedArrayBuffer
        enableSharedArrayBuffer: true,
        // 启用跨域隔离
        crossOriginIsolated: false
      }
    })

    // 设置窗口标题
    this.window.setTitle('犇犇数字员工助手')

    // 加载应用
    const startUrl = isDev
      ? 'http://localhost:6913'
      : `file://${join(__dirname, '../../../dist/index.html')}`

    console.log('Loading main window URL:', startUrl)
    this.window.loadURL(startUrl)

    // 开发环境下打开开发者工具
    if (isDev) {
      this.window.webContents.openDevTools()
    }

    // 添加更多调试事件
    this.window.webContents.on('did-start-loading', () => {
      console.log('Main window started loading')
    })

    this.window.webContents.on('did-finish-load', () => {
      console.log('Main window finished loading')
    })

    this.window.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('Main window failed to load:', errorCode, errorDescription, validatedURL)
    })

    // 窗口准备显示时显示窗口
    this.window.once('ready-to-show', () => {
      console.log('Main window ready to show')
      this.window.show()
      console.log('Main window shown, isVisible:', this.window.isVisible())

      // 设置全局变量以便其他模块访问
      global.mainWindow = this.window
    })

    // 强制显示窗口（备用方案）
    setTimeout(() => {
      if (this.window && !this.window.isDestroyed() && !this.window.isVisible()) {
        console.log('Force showing main window after timeout')
        this.window.show()
      }
    }, 3000)

    // 窗口关闭时的处理
    this.window.on('closed', () => {
      console.log('Main window closed')
      this.window = null
      global.mainWindow = null // 清空全局变量
    })

    // 修改窗口关闭行为，隐藏到托盘而不是退出
    this.window.on('close', (event) => {
      if (!this.appManager.app.isQuiting) {
        event.preventDefault()
        this.window.hide()

        // 首次隐藏时显示提示
        if (!this.appManager.hasShownTrayNotification && this.appManager.trayManager?.tray) {
          this.appManager.trayManager.tray.displayBalloon({
            iconType: 'info',
            title: '犇犇数字员工助手',
            content: '应用已隐藏到系统托盘，点击托盘图标可重新打开'
          })
          this.appManager.hasShownTrayNotification = true
        }
      }
    })

    // 添加网页加载错误处理
    this.window.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('Main window failed to load:', errorCode, errorDescription, validatedURL)
    })

    // 在主窗口加载完成后应用反向缩放
    this.window.webContents.once('did-finish-load', () => {
      console.log('主窗口加载完成，应用反向缩放以抵消Windows缩放:', this.appManager.displayScale)
      this.appManager.applyWindowScale(this.window)
    })

    return this.window
  }

  /**
   * 显示窗口
   */
  show() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show()
      this.window.focus()
    } else {
      this.createWindow()
    }
  }

  /**
   * 隐藏窗口
   */
  hide() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide()
    }
  }

  /**
   * 重新定位窗口
   */
  reposition() {
    if (!this.window || this.window.isDestroyed()) return

    try {
      let screenWidth = 1920, screenHeight = 1080
      try {
        if (screen && screen.getPrimaryDisplay) {
          const display = screen.getPrimaryDisplay()
          screenWidth = display.workAreaSize.width
          screenHeight = display.workAreaSize.height
        }
      } catch (screenError) {
        console.warn('Failed to get screen info for repositioning, using defaults')
      }

      const [windowWidth, windowHeight] = this.window.getSize()

      const centerX = Math.round((screenWidth - windowWidth) / 2) + 1000
      const centerY = Math.round((screenHeight - windowHeight) / 2)

      console.log(`重新定位主窗口到: x=${centerX}, y=${centerY}`)
      this.window.setPosition(centerX, centerY)
    } catch (error) {
      console.error('重新定位主窗口失败:', error)
    }
  }

  /**
   * 获取窗口实例
   */
  getWindow() {
    return this.window
  }

  /**
   * 检查窗口是否存在且未销毁
   */
  isValid() {
    return this.window && !this.window.isDestroyed()
  }

  /**
   * 销毁窗口
   */
  destroy() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy()
      this.window = null
      global.mainWindow = null
    }
  }
}

module.exports = MainWindowManager
