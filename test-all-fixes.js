/**
 * 测试所有修复是否有效
 * 验证重构后的main.js和所有修复的功能
 */

const fs = require('fs')

class AllFixesTester {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️'
    }[type] || '📋'
    
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  addResult(testName, passed, details = '') {
    this.results.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    })
  }

  // 测试1: 知识库目录选择修复
  testKnowledgeDirectoryFix() {
    this.log('测试知识库目录选择修复...')
    
    try {
      const knowledgeClientPath = 'src/renderer/utils/knowledge/knowledgeClient.js'
      const content = fs.readFileSync(knowledgeClientPath, 'utf8')
      
      const checks = [
        content.includes('result.path'),
        content.includes('result.filePaths'),
        content.includes('兼容新旧两种格式')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('知识库目录选择修复', passed, 
        passed ? '格式兼容性修复正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('知识库目录选择修复', false, error.message)
      return false
    }
  }

  // 测试2: 知识库搜索修复
  testKnowledgeSearchFix() {
    this.log('测试知识库搜索修复...')
    
    try {
      const knowledgeClientPath = 'src/renderer/utils/knowledge/knowledgeClient.js'
      const content = fs.readFileSync(knowledgeClientPath, 'utf8')
      
      const checks = [
        content.includes('response.success'),
        content.includes('response.results'),
        content.includes('Array.isArray(searchResults)')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('知识库搜索修复', passed, 
        passed ? '响应格式处理正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('知识库搜索修复', false, error.message)
      return false
    }
  }

  // 测试3: 邮件配置持久化修复
  testEmailConfigFix() {
    this.log('测试邮件配置持久化修复...')
    
    try {
      const emailConfigPath = 'src/renderer/components/EmailConfig.vue'
      const content = fs.readFileSync(emailConfigPath, 'utf8')
      
      const checks = [
        content.includes('response.success'),
        content.includes('response.config'),
        content.includes('邮件配置加载成功')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('邮件配置持久化修复', passed, 
        passed ? '配置加载逻辑正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('邮件配置持久化修复', false, error.message)
      return false
    }
  }

  // 测试4: 知识库清空功能修复
  testKnowledgeClearFix() {
    this.log('测试知识库清空功能修复...')
    
    try {
      const knowledgeIPCPath = 'src/main/ipc/knowledge.js'
      const content = fs.readFileSync(knowledgeIPCPath, 'utf8')
      
      const checks = [
        content.includes('clearKnowledgeBase()'),
        !content.includes('clearKnowledge()')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('知识库清空功能修复', passed, 
        passed ? '函数调用名称正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('知识库清空功能修复', false, error.message)
      return false
    }
  }

  // 测试5: MCP工具调用路由修复
  testMCPToolRoutingFix() {
    this.log('测试MCP工具调用路由修复...')
    
    try {
      const mcpIPCPath = 'src/main/ipc/mcp.js'
      const mcpManagerPath = 'src/main/mcp/clientManager.js'
      const filesystemMCPPath = 'src/main/mcp/filesystem.js'
      
      const mcpIPCContent = fs.readFileSync(mcpIPCPath, 'utf8')
      const mcpManagerContent = fs.readFileSync(mcpManagerPath, 'utf8')
      const filesystemContent = fs.readFileSync(filesystemMCPPath, 'utf8')
      
      const checks = [
        mcpIPCContent.includes('callRealMCPTool(toolName, args)'),
        mcpIPCContent.includes('toolName: toolName'),
        !mcpIPCContent.includes('toolType: toolType'),
        mcpManagerContent.includes('search_files'),
        filesystemContent.includes('case \'search_files\'')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('MCP工具调用路由修复', passed, 
        passed ? '工具路由和别名支持正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('MCP工具调用路由修复', false, error.message)
      return false
    }
  }

  // 测试6: 浏览器MCP降级方案
  testBrowserMCPFallback() {
    this.log('测试浏览器MCP降级方案...')
    
    try {
      const browserMCPPath = 'src/main/mcp/browser.js'
      const content = fs.readFileSync(browserMCPPath, 'utf8')
      
      const checks = [
        content.includes('启用浏览器MCP降级方案'),
        content.includes('callToolFallback'),
        content.includes('fallbackMode: true'),
        content.includes('shell.openExternal')
      ]
      
      const passed = checks.every(check => check)
      this.addResult('浏览器MCP降级方案', passed, 
        passed ? '降级方案实现正确' : '修复不完整')
      
      return passed
    } catch (error) {
      this.addResult('浏览器MCP降级方案', false, error.message)
      return false
    }
  }

  // 生成测试报告
  generateReport() {
    const endTime = Date.now()
    const duration = (endTime - this.startTime) / 1000

    this.log('\n=== 所有修复验证报告 ===')
    this.log(`测试时间: ${duration.toFixed(2)}秒`)
    
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    this.log(`总测试项: ${totalTests}`)
    this.log(`通过: ${passedTests}`, 'success')
    this.log(`失败: ${failedTests}`, failedTests > 0 ? 'error' : 'success')
    this.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

    this.log('\n=== 详细结果 ===')
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌'
      this.log(`${status} ${result.name}: ${result.details}`)
    })

    // 生成建议
    this.log('\n=== 修复状态总结 ===')
    if (failedTests === 0) {
      this.log('🎉 所有修复都已正确实施！', 'success')
      this.log('✅ 重构后的main.js功能完整', 'success')
      this.log('✅ 所有发现的问题都已解决', 'success')
      this.log('✅ 应用可以正常发布使用', 'success')
    } else {
      this.log('⚠️ 存在未完成的修复，需要进一步检查', 'warning')
    }

    // 保存报告到文件
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: duration,
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        passRate: ((passedTests / totalTests) * 100).toFixed(1)
      },
      results: this.results
    }

    fs.writeFileSync('all-fixes-report.json', JSON.stringify(reportData, null, 2))
    this.log('修复验证报告已保存到 all-fixes-report.json', 'success')

    return failedTests === 0
  }

  // 运行所有测试
  async runAllTests() {
    this.log('开始验证所有修复...')
    
    const tests = [
      () => this.testKnowledgeDirectoryFix(),
      () => this.testKnowledgeSearchFix(),
      () => this.testEmailConfigFix(),
      () => this.testKnowledgeClearFix(),
      () => this.testMCPToolRoutingFix(),
      () => this.testBrowserMCPFallback()
    ]

    for (const test of tests) {
      try {
        await test()
      } catch (error) {
        this.log(`测试执行失败: ${error.message}`, 'error')
      }
    }

    return this.generateReport()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new AllFixesTester()
  tester.runAllTests().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('测试运行失败:', error)
    process.exit(1)
  })
}

module.exports = AllFixesTester
