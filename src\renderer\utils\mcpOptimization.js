// MCP工具调用优化配置和工具函数

/**
 * MCP工具调用优化参数
 */
export const MCP_OPTIMIZATION_CONFIG = {
  // 模型参数优化
  modelParams: {
    temperature: 0.1,        // 降低温度提高工具调用一致性
    top_p: 0.2,             // 限制采样范围
    frequency_penalty: 0.1,  // 轻微惩罚重复
    presence_penalty: 0.1,   // 鼓励使用新的工具调用模式
    max_tokens: 2000        // 足够的token数量
  },
  
  // 重试配置
  retry: {
    maxAttempts: 2,         // 最大重试次数
    retryTemperature: 0.1,  // 重试时使用更低的温度
    retryMaxTokens: 1500    // 重试时的token限制
  },
  
  // 工具调用检测配置
  detection: {
    enableIntentAnalysis: true,    // 启用意图分析
    enableToolGuidance: true,      // 启用工具引导
    enableRetryOnMiss: true        // 启用工具调用缺失时的重试
  }
}

/**
 * 工具调用关键词映射
 */
export const TOOL_INTENT_KEYWORDS = {
  search_files: ['搜索', '查找', '找文件', '文件', '文档', '寻找'],
  open_file: ['打开', '开启', '启动', '运行', '查看'],
  read_file: ['读取', '查看内容', '看看', '内容', '读'],
  browser_navigate: ['打开网站', '访问', '浏览', '网站', '链接', 'http', 'www'],
  baidu_search: ['百度搜索', '百度查询', '百度一下', '用百度搜索', '用百度查询', '用百度', '百度'],
  query_flights: ['机票', '航班', '飞机', '订票', '出差', '旅行', '航空'],
  get_weather_forecast: ['天气', '气温', '温度', '预报', '下雨', '晴天', '阴天'],
  send_email: ['发邮件', '写邮件', '邮件', '发送', '写信'],
  list_email: ['查邮件', '收件', '邮箱', '未读', '新邮件']
}

/**
 * 检查是否应该触发百度搜索
 * @param {string} userMessage - 用户消息
 * @returns {boolean} 是否应该触发百度搜索
 */
function shouldTriggerBaiduSearch(userMessage) {
  const message = userMessage.toLowerCase()

  // 必须包含"百度"字样
  const hasBaiduKeyword = message.includes('百度')

  if (!hasBaiduKeyword) {
    return false
  }

  // 检查是否是搜索相关的表达
  const searchPatterns = [
    '百度搜索',
    '百度查询',
    '百度一下',
    '用百度搜索',
    '用百度查询',
    '用百度',
    '百度.*搜索',
    '百度.*查询',
    '百度.*找'
  ]

  return searchPatterns.some(pattern => {
    const regex = new RegExp(pattern)
    return regex.test(message)
  })
}

/**
 * 分析用户消息意图
 * @param {string} userMessage - 用户消息
 * @returns {Object} 意图分析结果
 */
export function analyzeToolIntent(userMessage) {
  const message = userMessage.toLowerCase()
  const detectedTools = []
  const confidence = {}

  // 遍历所有工具的关键词
  for (const [toolName, keywords] of Object.entries(TOOL_INTENT_KEYWORDS)) {
    // 对百度搜索进行特殊处理
    if (toolName === 'baidu_search') {
      if (shouldTriggerBaiduSearch(userMessage)) {
        detectedTools.push(toolName)
        confidence[toolName] = 1.0 // 高置信度
      }
      continue
    }

    const matchedKeywords = keywords.filter(keyword => message.includes(keyword))

    if (matchedKeywords.length > 0) {
      detectedTools.push(toolName)
      confidence[toolName] = matchedKeywords.length / keywords.length
    }
  }

  // 按置信度排序
  detectedTools.sort((a, b) => confidence[b] - confidence[a])

  return {
    detectedTools,
    confidence,
    hasToolIntent: detectedTools.length > 0,
    primaryTool: detectedTools[0] || null,
    originalMessage: userMessage,
    baiduSearchTriggered: detectedTools.includes('baidu_search')
  }
}



/**
 * 检查是否是模糊的搜索请求（需要进一步澄清）
 * @param {string} userMessage - 用户消息
 * @param {Array} conversationHistory - 对话历史
 * @returns {Object} 检查结果
 */
export function checkAmbiguousSearchRequest(userMessage, conversationHistory = []) {
  const message = userMessage.toLowerCase()

  // 检查是否包含模糊的搜索词汇但没有明确指向
  const ambiguousSearchKeywords = ['搜索']
  const hasAmbiguousSearch = ambiguousSearchKeywords.some(keyword => message.includes(keyword))

  if (!hasAmbiguousSearch) {
    return { isAmbiguous: false }
  }

  // 检查是否有明确的指向
  const specificTargets = [
    '文件', '文档', '天气', '机票', '航班', '邮件', '百度'
  ]
  const hasSpecificTarget = specificTargets.some(target => message.includes(target))

  // 如果有搜索词汇但没有明确指向，则认为是模糊请求
  const isAmbiguous = hasAmbiguousSearch && !hasSpecificTarget

  return {
    isAmbiguous,
    suggestedClarification: isAmbiguous ?
      "您是想要搜索文件、查询天气、机票信息，还是需要百度搜索？请告诉我具体需求。" :
      null
  }
}

/**
 * 生成工具调用引导提示
 * @param {Array} suggestedTools - 建议的工具列表
 * @param {string} userMessage - 用户原始消息
 * @returns {string} 引导提示
 */
export function generateToolGuidance(suggestedTools, userMessage) {
  if (!suggestedTools || suggestedTools.length === 0) {
    return ''
  }

  const toolDescriptions = {
    search_files: '搜索文件',
    open_file: '打开文件',
    read_file: '读取文件内容',
    browser_navigate: '打开网站',
    baidu_search: '百度搜索',
    query_flights: '查询机票',
    get_weather_forecast: '查询天气',
    send_email: '发送邮件',
    list_email: '查看邮件'
  }

  const descriptions = suggestedTools
    .map(tool => toolDescriptions[tool] || tool)
    .join('、')

  // 特别处理百度搜索的引导
  if (suggestedTools.includes('baidu_search')) {
    return `🎯 根据用户请求"${userMessage}"，检测到明确的百度搜索需求，建议调用baidu_search工具。请使用标准的function calling格式调用相应的工具函数。`
  }

  return `🎯 根据用户请求"${userMessage}"，建议调用以下工具：${descriptions}。请使用标准的function calling格式调用相应的工具函数。`
}

/**
 * 检查响应是否包含有效的工具调用
 * @param {Object} response - AI响应
 * @returns {boolean} 是否包含工具调用
 */
export function hasValidToolCalls(response) {
  return !!(response?.tool_calls && Array.isArray(response.tool_calls) && response.tool_calls.length > 0)
}

/**
 * 构建优化的请求参数
 * @param {Object} baseParams - 基础参数
 * @param {Object} intentAnalysis - 意图分析结果
 * @returns {Object} 优化后的请求参数
 */
export function buildOptimizedRequest(baseParams, intentAnalysis = null) {
  const optimizedParams = {
    ...baseParams,
    ...MCP_OPTIMIZATION_CONFIG.modelParams
  }
  
  // 如果检测到工具调用意图，进一步优化参数
  if (intentAnalysis?.hasToolIntent) {
    optimizedParams.temperature = Math.min(optimizedParams.temperature, 0.15)
    optimizedParams.tool_choice = "auto" // 确保工具选择为自动
    
    // 在系统消息中添加工具引导
    if (optimizedParams.messages && optimizedParams.messages[0]?.role === 'system') {
      const guidance = generateToolGuidance(intentAnalysis.detectedTools, intentAnalysis.originalMessage)
      if (guidance) {
        optimizedParams.messages[0].content += `\n\n${guidance}`
      }
    }
  }
  
  return optimizedParams
}

/**
 * 工具调用成功率统计
 */
export class ToolCallStats {
  constructor() {
    this.stats = {
      totalRequests: 0,
      toolCallRequests: 0,
      successfulToolCalls: 0,
      failedToolCalls: 0,
      retryAttempts: 0,
      retrySuccesses: 0
    }
  }
  
  recordRequest(hasToolIntent = false) {
    this.stats.totalRequests++
    if (hasToolIntent) {
      this.stats.toolCallRequests++
    }
  }
  
  recordToolCallSuccess() {
    this.stats.successfulToolCalls++
  }
  
  recordToolCallFailure() {
    this.stats.failedToolCalls++
  }
  
  recordRetryAttempt() {
    this.stats.retryAttempts++
  }
  
  recordRetrySuccess() {
    this.stats.retrySuccesses++
  }
  
  getSuccessRate() {
    if (this.stats.toolCallRequests === 0) return 0
    return this.stats.successfulToolCalls / this.stats.toolCallRequests
  }
  
  getRetrySuccessRate() {
    if (this.stats.retryAttempts === 0) return 0
    return this.stats.retrySuccesses / this.stats.retryAttempts
  }
  
  getReport() {
    return {
      ...this.stats,
      successRate: this.getSuccessRate(),
      retrySuccessRate: this.getRetrySuccessRate()
    }
  }
}

// 全局统计实例
export const toolCallStats = new ToolCallStats()

/**
 * 常见工具调用错误的修复建议
 */
export const TOOL_CALL_ERROR_FIXES = {
  'parameter_missing': '请检查工具调用参数是否完整',
  'invalid_format': '请使用标准的function calling格式',
  'tool_not_found': '请检查工具名称是否正确',
  'execution_failed': '工具执行失败，请稍后重试',
  'timeout': '工具调用超时，请检查网络连接'
}

/**
 * 生成工具调用错误的用户友好提示
 * @param {string} errorType - 错误类型
 * @param {string} toolName - 工具名称
 * @returns {string} 用户友好的错误提示
 */
export function generateErrorMessage(errorType, toolName = '') {
  const fix = TOOL_CALL_ERROR_FIXES[errorType] || '操作失败，请稍后重试'
  return toolName ? `${toolName}工具${fix}` : fix
}
