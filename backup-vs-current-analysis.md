# Backup vs Current Code Analysis

## 关键差异分析

### 1. 文件搜索功能差异

#### Backup实现 (main.js.backup)
- 使用真实的MCP客户端 `@modelcontextprotocol/server-filesystem`
- 通过NPX启动外部MCP服务器进程
- 支持用户配置的自定义路径
- 使用真实的MCP协议进行通信
- 参数格式：`{ query, path, fuzzy_match: true }`

#### Current实现 (src/main/mcp/filesystem.js)
- 使用内置的Node.js文件搜索
- 直接使用fs模块递归搜索
- 支持用户配置路径但实现方式不同
- 参数格式：`{ query, directory }`

### 2. 上下文清空策略

#### Backup实现
- 在FloatingCharacter.vue中实现
- 复杂的智能清空策略
- 基于工具类型和操作链判断
- 支持调用前和调用后清空

#### Current实现
- 相同的实现在FloatingCharacter.vue中
- 策略逻辑基本一致
- ✅ 无需修改

### 3. MCP工具路由

#### Backup实现
- 在main.js中集中处理所有工具路由
- 复杂的if-else逻辑判断工具类型
- 每个工具都有详细的错误处理

#### Current实现
- 模块化设计，每个服务独立
- 在clientManager.js中统一路由
- 简化的错误处理

## 需要修复的问题

### 1. 文件搜索功能不一致 ❌
**问题**: 当前实现使用本地搜索，backup使用真实MCP
**影响**: 搜索结果可能不同，功能不完整
**修复**: 需要实现真实的MCP文件系统服务器连接

### 2. 用户配置路径处理 ❌
**问题**: 当前实现的路径配置逻辑与backup不完全一致
**影响**: 用户配置的自定义路径可能无法正确使用
**修复**: 需要完全对齐路径配置逻辑

### 3. MCP服务器进程管理 ❌
**问题**: 当前实现缺少真实的MCP服务器进程管理
**影响**: 无法启动和管理外部MCP服务器
**修复**: 需要实现MCP服务器进程启动和管理

## 修复优先级

### 高优先级
1. **文件搜索功能** - 核心功能不一致
2. **用户配置路径** - 影响用户体验

### 中优先级
3. **MCP服务器管理** - 架构完整性

### 低优先级
4. **错误处理优化** - 用户体验改进

## 建议修复方案

### 方案1: 完全对齐backup实现
- 恢复真实的MCP服务器连接
- 实现完整的进程管理
- 保持与backup完全一致

### 方案2: 渐进式修复
- 先修复文件搜索功能
- 逐步完善其他功能
- 保持模块化架构

### 方案3: 混合方案 (推荐)
- 保持当前的模块化架构
- 在filesystem.js中实现真实MCP连接
- 确保功能完全一致但代码更清晰
