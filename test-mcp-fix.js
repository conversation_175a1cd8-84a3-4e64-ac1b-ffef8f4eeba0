/**
 * 测试MCP工具调用修复
 * 验证修复后的IPC处理程序是否正常工作
 */

const fs = require('fs')

function testMCPIPCHandler() {
  console.log('🧪 开始测试MCP IPC处理程序修复...')
  
  try {
    // 读取修复后的IPC处理程序文件
    const mcpIPCPath = 'src/main/ipc/mcp.js'
    const mcpIPCContent = fs.readFileSync(mcpIPCPath, 'utf8')
    
    console.log('📋 检查修复内容...')
    
    // 检查是否包含正确的修复
    const checks = [
      {
        name: '使用callRealMCPTool方法',
        pattern: 'callRealMCPTool(toolName, args)',
        found: mcpIPCContent.includes('callRealMCPTool(toolName, args)')
      },
      {
        name: '移除了toolType解析逻辑',
        pattern: 'parseToolName',
        found: !mcpIPCContent.includes('parseToolName')
      },
      {
        name: '错误处理使用toolName',
        pattern: 'toolName: toolName',
        found: mcpIPCContent.includes('toolName: toolName')
      },
      {
        name: '移除了toolType引用',
        pattern: 'toolType: toolType',
        found: !mcpIPCContent.includes('toolType: toolType')
      }
    ]
    
    let allPassed = true
    checks.forEach(check => {
      if (check.found) {
        console.log(`✅ ${check.name}`)
      } else {
        console.log(`❌ ${check.name}`)
        allPassed = false
      }
    })
    
    // 检查关键代码片段
    console.log('\n📋 检查关键代码片段...')
    
    if (mcpIPCContent.includes('execute-mcp-tool')) {
      console.log('✅ IPC处理程序已注册')
    } else {
      console.log('❌ IPC处理程序未找到')
      allPassed = false
    }
    
    if (mcpIPCContent.includes('async (event, toolName, args)')) {
      console.log('✅ 参数签名正确')
    } else {
      console.log('❌ 参数签名不正确')
      allPassed = false
    }
    
    console.log('\n📊 修复验证结果:')
    console.log(`总检查项: ${checks.length + 2}`)
    console.log(`通过项: ${allPassed ? checks.length + 2 : '部分'}`)
    
    if (allPassed) {
      console.log('🎉 所有修复验证通过！')
    } else {
      console.log('⚠️ 存在未通过的检查项')
    }
    
    return allPassed
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testMCPIPCHandler().then ? 
    testMCPIPCHandler().then(success => {
      console.log(success ? '✅ 测试通过' : '❌ 测试失败')
      process.exit(success ? 0 : 1)
    }).catch(error => {
      console.error('测试运行失败:', error)
      process.exit(1)
    }) :
    (() => {
      const success = testMCPIPCHandler()
      console.log(success ? '✅ 测试通过' : '❌ 测试失败')
      process.exit(success ? 0 : 1)
    })()
}

module.exports = { testMCPIPCHandler }
