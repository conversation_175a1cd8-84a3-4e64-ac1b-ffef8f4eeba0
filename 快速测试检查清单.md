# AI CogniDesk Client - 快速测试检查清单

## 🚀 重构后main.js核心功能快速验证

### ⚡ 1分钟快速检查 (必测项目)
- [ ] **应用启动**: `npm run electron:dev` 正常启动
- [ ] **主窗口显示**: 主窗口正常创建和显示
- [ ] **控制台转发**: 主进程日志在浏览器控制台可见
- [ ] **登录功能**: 用户可以正常登录
- [ ] **服务初始化**: 登录后各服务正常初始化

### ⚡ 5分钟核心功能检查
- [ ] **窗口管理**: 主窗口、悬浮窗、托盘功能正常
- [ ] **缩放适配**: 在不同Windows缩放比例下显示正常
- [ ] **单实例检查**: 重复启动应用时单实例锁生效
- [ ] **协议处理**: `ai-cognidesk://` 协议链接正常处理
- [ ] **IPC通信**: 主进程与渲染进程通信正常

### ⚡ 15分钟服务功能检查
- [ ] **知识库服务**: 知识库初始化和基本功能
  - [ ] 知识库目录选择功能正常
  - [ ] 知识库搜索功能正常
  - [ ] 知识库清空功能正常
- [ ] **MCP管理器**: MCP客户端连接状态正常
  - [ ] MCP工具调用路由正常（如browser_navigate）
  - [ ] 各MCP服务工具可正常执行
- [ ] **邮件服务**: 邮件配置和基本功能（如已配置）
- [ ] **日历服务**: 日历服务初始化正常
- [ ] **子进程管理**: Python子进程正常启动且窗口隐藏

## 🔧 重构验证重点

### AppManager类功能验证
- [ ] **构造函数**: 所有属性正确初始化
- [ ] **缩放检测**: `getWindowsScaleFactor()` 返回正确值
- [ ] **权限设置**: `setupSessionPermissions()` 正确配置
- [ ] **IPC设置**: `setupIPC()` 通过IPC管理器注册成功
- [ ] **服务初始化**: `initializeServices()` 按正确顺序初始化

### 窗口管理器集成验证
- [ ] **窗口管理器**: WindowManager正确初始化
- [ ] **窗口创建**: 通过窗口管理器创建窗口
- [ ] **窗口操作**: 窗口显示、隐藏、定位功能
- [ ] **缩放应用**: 窗口缩放正确应用

### IPC管理器集成验证
- [ ] **IPC管理器**: IPCManager正确初始化
- [ ] **处理程序注册**: 所有IPC处理程序正确注册
- [ ] **消息路由**: IPC消息正确路由到对应处理程序

## 🐛 常见问题检查

### 启动问题
- [ ] **依赖缺失**: 检查是否所有npm依赖已安装
- [ ] **Python环境**: 检查Python环境是否正确配置
- [ ] **端口冲突**: 检查开发服务器端口是否被占用
- [ ] **权限问题**: 检查文件读写权限

### 功能问题
- [ ] **服务连接失败**: 检查MCP服务是否正常启动
- [ ] **网络连接问题**: 检查网络连接和防火墙设置
- [ ] **配置文件问题**: 检查配置文件格式和内容
- [ ] **Token问题**: 检查用户认证token是否有效

### 性能问题
- [ ] **内存泄漏**: 长时间运行后内存使用是否正常
- [ ] **CPU占用**: 空闲状态CPU占用是否合理
- [ ] **响应速度**: 界面操作响应是否及时
- [ ] **资源清理**: 应用退出时资源是否正确清理

## 📋 测试执行步骤

### 第一轮：基础功能测试 (10分钟)
1. **清理环境**
   ```bash
   npm run clean
   npm install
   ```

2. **启动测试**
   ```bash
   npm run electron:dev
   ```

3. **基础验证**
   - 检查应用启动
   - 检查主窗口显示
   - 检查控制台输出
   - 检查登录功能

### 第二轮：服务集成测试 (20分钟)
1. **登录应用**
   - 使用有效账号登录
   - 观察服务初始化过程

2. **服务验证**
   - 检查知识库服务状态
   - 检查MCP服务连接
   - 检查邮件服务（如已配置）
   - 检查日历服务

3. **功能测试**
   - 测试基本AI对话功能
   - 测试知识库查询
   - 测试MCP工具调用

### 第三轮：稳定性测试 (30分钟)
1. **压力测试**
   - 连续使用30分钟
   - 执行各种操作
   - 监控资源使用

2. **异常测试**
   - 断网重连测试
   - 强制关闭重启测试
   - 多次登录登出测试

3. **边界测试**
   - 最小化恢复测试
   - 多窗口切换测试
   - 系统缩放变更测试

## 🚨 关键检查点

### 必须通过的测试
- [ ] **应用正常启动**: 无崩溃，无严重错误
- [ ] **用户可以登录**: 登录流程完整可用
- [ ] **核心功能可用**: AI对话、知识库等核心功能正常
- [ ] **窗口管理正常**: 主窗口、悬浮窗、托盘功能正常
- [ ] **应用正常退出**: 退出时资源正确清理

### 重要但可接受问题的测试
- [ ] **邮件服务**: 可能因配置问题失败，但不影响核心功能
- [ ] **日历服务**: 可能因权限问题失败，但不影响核心功能
- [ ] **部分MCP服务**: 个别MCP服务连接失败可接受
- [ ] **网络相关功能**: 在网络环境限制下可能失败

## 📊 测试结果记录

### 快速测试结果
```
测试时间: ___________
测试人员: ___________

✅ 通过项目: ___/___
❌ 失败项目: ___/___
⚠️  警告项目: ___/___

总体评价: 
□ 优秀 - 所有测试通过
□ 良好 - 核心功能正常，少量非关键问题
□ 一般 - 核心功能正常，存在一些问题需要修复
□ 差 - 存在严重问题，需要重大修复
```

### 主要问题记录
1. **问题1**: ________________
   - 严重程度: □高 □中 □低
   - 影响功能: ________________
   - 建议修复: ________________

2. **问题2**: ________________
   - 严重程度: □高 □中 □低
   - 影响功能: ________________
   - 建议修复: ________________

3. **问题3**: ________________
   - 严重程度: □高 □中 □低
   - 影响功能: ________________
   - 建议修复: ________________

## 🎯 发布决策

### 发布建议
- [ ] **立即发布**: 所有核心功能正常，无严重问题
- [ ] **修复后发布**: 存在需要修复的问题，但不影响基本使用
- [ ] **延期发布**: 存在严重问题，需要重大修复

### 风险评估
- **高风险问题**: ___个 (阻止发布)
- **中风险问题**: ___个 (建议修复)
- **低风险问题**: ___个 (可接受)

---
**注意**: 此检查清单用于快速验证重构后的main.js功能，详细测试请参考《测试文档-main.js重构后全量测试.md》
