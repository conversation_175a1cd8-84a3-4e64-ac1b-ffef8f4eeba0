/**
 * MCP服务状态检查工具
 * 用于诊断MCP相关功能问题
 */

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

class MCPStatusChecker {
  constructor() {
    this.results = []
    this.startTime = Date.now()
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = {
      'info': '📋',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️'
    }[type] || '📋'
    
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  addResult(testName, passed, details = '') {
    this.results.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    })
  }

  // 检查MCP服务目录结构
  async checkMCPDirectories() {
    this.log('检查MCP服务目录结构...')
    
    const mcpDir = 'mcp-servers'
    const expectedServices = [
      'email-server',
      'weather-server',
      'office-bot'
    ]

    let allServicesExist = true
    let foundServices = []

    if (!fs.existsSync(mcpDir)) {
      this.log('MCP服务根目录不存在', 'error')
      this.addResult('MCP目录结构检查', false, 'MCP服务根目录不存在')
      return false
    }

    for (const service of expectedServices) {
      const servicePath = path.join(mcpDir, service)
      if (fs.existsSync(servicePath)) {
        this.log(`MCP服务目录存在: ${service}`, 'success')
        foundServices.push(service)
        
        // 检查服务的关键文件
        const keyFiles = this.getServiceKeyFiles(service)
        for (const file of keyFiles) {
          const filePath = path.join(servicePath, file)
          if (fs.existsSync(filePath)) {
            this.log(`  关键文件存在: ${file}`, 'success')
          } else {
            this.log(`  关键文件缺失: ${file}`, 'warning')
          }
        }
      } else {
        this.log(`MCP服务目录缺失: ${service}`, 'error')
        allServicesExist = false
      }
    }

    this.addResult('MCP目录结构检查', foundServices.length > 0, 
      `找到${foundServices.length}/${expectedServices.length}个服务目录`)
    
    return foundServices.length > 0
  }

  getServiceKeyFiles(service) {
    const keyFiles = {
      'email-server': ['main.py', 'requirements.txt'],
      'weather-server': ['pyproject.toml', 'requirements.txt'],
      'office-bot': ['main.py', 'requirements.txt']
    }
    return keyFiles[service] || ['main.py']
  }

  // 检查MCP配置文件
  async checkMCPConfig() {
    this.log('检查MCP配置文件...')
    
    const configFile = 'mcp-config.json'
    if (!fs.existsSync(configFile)) {
      this.log('MCP配置文件不存在', 'error')
      this.addResult('MCP配置文件检查', false, 'mcp-config.json不存在')
      return false
    }

    try {
      const configContent = fs.readFileSync(configFile, 'utf8')
      const config = JSON.parse(configContent)
      
      this.log('MCP配置文件格式正确', 'success')
      
      // 检查配置结构
      if (config.mcpServers && typeof config.mcpServers === 'object') {
        const serverCount = Object.keys(config.mcpServers).length
        this.log(`配置了${serverCount}个MCP服务器`, 'success')
        
        // 检查每个服务器配置
        for (const [serverName, serverConfig] of Object.entries(config.mcpServers)) {
          this.log(`  服务器: ${serverName}`)
          if (serverConfig.command) {
            this.log(`    命令: ${serverConfig.command}`, 'success')
          } else {
            this.log(`    缺少命令配置`, 'warning')
          }
          if (serverConfig.args) {
            this.log(`    参数: ${serverConfig.args.length}个`, 'success')
          }
        }
        
        this.addResult('MCP配置文件检查', true, `配置了${serverCount}个服务器`)
        return true
      } else {
        this.log('MCP配置文件结构不正确', 'error')
        this.addResult('MCP配置文件检查', false, '配置文件结构不正确')
        return false
      }
    } catch (error) {
      this.log(`MCP配置文件解析失败: ${error.message}`, 'error')
      this.addResult('MCP配置文件检查', false, `解析失败: ${error.message}`)
      return false
    }
  }

  // 检查Python环境
  async checkPythonEnvironment() {
    this.log('检查Python环境...')
    
    return new Promise((resolve) => {
      const python = spawn('python', ['--version'], { shell: true })
      let output = ''
      let error = ''

      python.stdout.on('data', (data) => {
        output += data.toString()
      })

      python.stderr.on('data', (data) => {
        error += data.toString()
      })

      python.on('close', (code) => {
        if (code === 0) {
          const version = (output + error).trim()
          this.log(`Python版本: ${version}`, 'success')
          this.addResult('Python环境检查', true, version)
          resolve(true)
        } else {
          this.log('Python未安装或不可用', 'error')
          this.addResult('Python环境检查', false, 'Python不可用')
          resolve(false)
        }
      })

      python.on('error', (err) => {
        this.log(`Python检查失败: ${err.message}`, 'error')
        this.addResult('Python环境检查', false, err.message)
        resolve(false)
      })
    })
  }

  // 检查Node.js依赖
  async checkNodeDependencies() {
    this.log('检查Node.js依赖...')
    
    const packageJsonPath = 'package.json'
    if (!fs.existsSync(packageJsonPath)) {
      this.log('package.json不存在', 'error')
      this.addResult('Node.js依赖检查', false, 'package.json不存在')
      return false
    }

    try {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
      const mcpDeps = [
        '@modelcontextprotocol/sdk',
        '@playwright/mcp',
        'playwright'
      ]

      let allDepsExist = true
      for (const dep of mcpDeps) {
        if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
          this.log(`依赖存在: ${dep}`, 'success')
        } else {
          this.log(`依赖缺失: ${dep}`, 'error')
          allDepsExist = false
        }
      }

      // 检查node_modules
      if (fs.existsSync('node_modules')) {
        this.log('node_modules目录存在', 'success')
      } else {
        this.log('node_modules目录不存在，需要运行npm install', 'warning')
        allDepsExist = false
      }

      this.addResult('Node.js依赖检查', allDepsExist, 
        allDepsExist ? '所有MCP相关依赖都存在' : '存在缺失的依赖')
      
      return allDepsExist
    } catch (error) {
      this.log(`检查依赖失败: ${error.message}`, 'error')
      this.addResult('Node.js依赖检查', false, error.message)
      return false
    }
  }

  // 检查端口占用
  async checkPortUsage() {
    this.log('检查端口占用情况...')
    
    // 这里可以检查常用的MCP服务端口
    // 由于大多数MCP服务使用STDIO，这个检查主要是确保没有冲突
    
    this.addResult('端口检查', true, '使用STDIO通信，无端口冲突')
    return true
  }

  // 生成诊断报告
  generateReport() {
    const endTime = Date.now()
    const duration = (endTime - this.startTime) / 1000

    this.log('\n=== MCP服务状态检查报告 ===')
    this.log(`检查时间: ${duration.toFixed(2)}秒`)
    
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    this.log(`总检查项: ${totalTests}`)
    this.log(`通过: ${passedTests}`, 'success')
    this.log(`失败: ${failedTests}`, failedTests > 0 ? 'error' : 'success')
    this.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

    this.log('\n=== 详细结果 ===')
    this.results.forEach(result => {
      const status = result.passed ? '✅' : '❌'
      this.log(`${status} ${result.name}: ${result.details}`)
    })

    // 生成建议
    this.log('\n=== 诊断建议 ===')
    if (failedTests === 0) {
      this.log('🎉 所有MCP相关检查都通过了！', 'success')
      this.log('如果仍有问题，请检查应用运行时的MCP连接状态。', 'info')
    } else {
      this.log('🔧 发现以下问题需要修复:', 'warning')
      
      this.results.filter(r => !r.passed).forEach(result => {
        this.log(`  - ${result.name}: ${result.details}`, 'error')
      })
      
      this.log('\n建议修复步骤:', 'info')
      this.log('1. 确保所有依赖已安装: npm install', 'info')
      this.log('2. 检查Python环境: python --version', 'info')
      this.log('3. 验证MCP服务目录结构', 'info')
      this.log('4. 检查mcp-config.json配置', 'info')
    }

    // 保存报告到文件
    const reportData = {
      timestamp: new Date().toISOString(),
      duration: duration,
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        passRate: ((passedTests / totalTests) * 100).toFixed(1)
      },
      results: this.results
    }

    fs.writeFileSync('mcp-status-report.json', JSON.stringify(reportData, null, 2))
    this.log('MCP状态检查报告已保存到 mcp-status-report.json', 'success')

    return failedTests === 0
  }

  // 运行所有检查
  async runAllChecks() {
    this.log('开始MCP服务状态检查...')
    
    const checks = [
      () => this.checkMCPDirectories(),
      () => this.checkMCPConfig(),
      () => this.checkPythonEnvironment(),
      () => this.checkNodeDependencies(),
      () => this.checkPortUsage()
    ]

    for (const check of checks) {
      try {
        await check()
      } catch (error) {
        this.log(`检查执行失败: ${error.message}`, 'error')
      }
    }

    return this.generateReport()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new MCPStatusChecker()
  checker.runAllChecks().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('MCP状态检查运行失败:', error)
    process.exit(1)
  })
}

module.exports = MCPStatusChecker
