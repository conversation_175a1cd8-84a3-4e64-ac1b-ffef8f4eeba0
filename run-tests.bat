@echo off
echo ========================================
echo AI CogniDesk Client - 重构后测试脚本
echo ========================================
echo.

echo 1. 运行自动化基础检查...
node test-main-refactor.js
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 自动化测试失败，请检查基础问题后再进行手动测试
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 自动化测试通过！
echo.
echo 2. 准备启动应用进行手动测试...
echo.
echo 请选择测试模式:
echo [1] 开发模式测试 (npm run electron:dev)
echo [2] 生产模式测试 (需要先构建)
echo [3] MCP服务状态检查
echo [4] 仅查看测试报告
echo [5] 退出
echo.
set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" (
    echo.
    echo 启动开发模式...
    echo 请在应用启动后参考以下文档进行手动测试:
    echo - 快速测试检查清单.md
    echo - 测试文档-main.js重构后全量测试.md
    echo.
    echo 按任意键启动应用...
    pause >nul
    npm run electron:dev
) else if "%choice%"=="2" (
    echo.
    echo 构建并启动生产模式...
    echo 正在构建应用...
    npm run electron:dist
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 构建完成，请手动运行 dist 目录中的应用程序
        echo 然后参考测试文档进行手动测试
    ) else (
        echo.
        echo ❌ 构建失败，请检查构建错误
    )
) else if "%choice%"=="3" (
    echo.
    echo 运行MCP服务状态检查...
    node mcp-status-checker.js
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ MCP服务状态检查完成，详细报告请查看 mcp-status-report.json
    ) else (
        echo.
        echo ❌ MCP服务状态检查发现问题，请查看上述输出和报告文件
    )
) else if "%choice%"=="4" (
    echo.
    echo 查看测试报告...
    if exist test-report.json (
        echo === 基础测试报告 ===
        type test-report.json
    ) else (
        echo 基础测试报告不存在，请先运行基础测试
    )
    echo.
    if exist mcp-status-report.json (
        echo === MCP状态检查报告 ===
        type mcp-status-report.json
    ) else (
        echo MCP状态检查报告不存在，请先运行MCP状态检查
    )
) else if "%choice%"=="5" (
    echo.
    echo 退出测试脚本
    exit /b 0
) else (
    echo.
    echo 无效选择，退出
    exit /b 1
)

echo.
pause
