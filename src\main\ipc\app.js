const { ipcMain, shell, app, screen } = require('electron')

/**
 * 应用相关的IPC处理程序
 */
class AppIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
  }

  /**
   * 注册所有应用相关的IPC处理程序
   */
  register() {
    // 打开URL
    ipcMain.handle('open-url', async (event, url) => {
      try {
        console.log('🔗 打开URL:', url)
        await shell.openExternal(url)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    // 获取应用版本
    ipcMain.handle('get-app-version', () => {
      const version = app.getVersion()
      console.log('🔄 获取应用版本:', version)
      return version
    })

    // 主进程状态消息通信
    ipcMain.handle('get-status', async (event) => {
      // 返回MCP客户端的连接状态
      const mcpStatus = this.appManager.mcpManager.getConnectionStatus()
      return mcpStatus
    })

    // 发送状态消息到主窗口
    ipcMain.handle('send-status-message', async (event, message) => {
      try {
        console.log('🔄 收到状态消息广播请求 (AppManager):', message)

        // 发送到主窗口
        if (this.appManager.windowManager.isMainWindowValid()) {
          this.appManager.windowManager.getMainWindow().webContents.send('status-message', message)
          console.log('🔄 已发送状态消息到主窗口:', message)
        }

        // 发送到悬浮窗
        if (this.appManager.windowManager.isFloatingWindowValid()) {
          this.appManager.windowManager.getFloatingWindow().webContents.send('status-message', message)
          console.log('🔄 已发送状态消息到悬浮窗:', message)
        }

        return { success: true }
      } catch (error) {
        console.error('发送状态消息失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取应用状态
    ipcMain.on('get-app-status', (event) => {
      // 获取应用状态并发送
      const status = {
        mcpInitialized: this.appManager.mcpManager.initialized,
        clients: Array.from(this.appManager.mcpManager.clients.keys()),
        lastUpdate: new Date().toISOString()
      }
      event.reply('app-status', status)
    })

    // 播放欢迎语音
    ipcMain.handle('speak-welcome-message', async (event, options) => {
      try {
        console.log('收到欢迎语音播放请求:', options)

        // 发送语音播报事件到渲染进程
        if (this.appManager.windowManager.isMainWindowValid()) {
          this.appManager.windowManager.getMainWindow().webContents.send('speak-text', {
            text: options.text || '',
            volume: options.volume || 1.0,
            rate: options.rate || 1.0,
            callback: options.callback || 'welcome-voice-completed'
          })
        }

        // 同时发送到悬浮窗
        if (this.appManager.windowManager.isFloatingWindowValid()) {
          this.appManager.windowManager.getFloatingWindow().webContents.send('speak-text', {
            text: options.text || '',
            volume: options.volume || 1.0,
            rate: options.rate || 1.0,
            callback: options.callback || 'welcome-voice-completed'
          })
        }

        return { success: true }
      } catch (error) {
        console.error('播放欢迎语音失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 隐藏主窗口
    ipcMain.on('hide-main-window', () => {
      this.appManager.windowManager.hideMainWindow()
    })

    // 关闭应用
    ipcMain.on('close-app', () => {
      app.quit()
    })

    // 获取路径
    ipcMain.handle('get-path', async (event, pathName) => {
      try {
        const path = app.getPath(pathName)
        console.log(`🧠 获取路径 ${pathName}: ${path}`)
        return path
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    // 打开文件
    ipcMain.handle('open-file', async (event, filePath, options = {}) => {
      try {
        console.log(`📂 [OPEN_FILE] 收到打开文件请求: "${filePath}"`, '选项:', options)

        if (!filePath) {
          const error = '缺少文件路径参数'
          console.error(`📂 [OPEN_FILE] ${error}`)
          return { success: false, error }
        }

        // 使用shell.openPath打开文件
        const result = await shell.openPath(filePath)
        
        if (result) {
          console.error(`📂 [OPEN_FILE] 打开文件失败: ${result}`)
          return { success: false, error: result }
        } else {
          console.log(`📂 [OPEN_FILE] 文件打开成功: "${filePath}"`)
          return { success: true }
        }
      } catch (error) {
        console.error(`📂 [OPEN_FILE] 打开文件异常:`, error)
        return { success: false, error: error.message }
      }
    })

    // 获取Windows缩放比例
    ipcMain.handle('get-windows-scale-factor', () => {
      try {
        const scaleFactor = this.appManager.getWindowsScaleFactor()
        console.log('📏 获取Windows缩放比例:', scaleFactor)
        return scaleFactor
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    // 处理通用事件通知
    ipcMain.handle('notifyEvent', async (event, eventType, eventData) => {
      console.log('📢 收到事件通知:', eventType, eventData)
      
      try {
        switch (eventType) {
          case 'model-changed':
            console.log('🔄 模型变更事件:', eventData)
            // 这里可以添加模型变更的处理逻辑
            break
          case 'config-updated':
            console.log('⚙️ 配置更新事件:', eventData)
            // 这里可以添加配置更新的处理逻辑
            break
          default:
            console.log('❓ 未知事件类型:', eventType)
        }
        
        return { success: true }
      } catch (error) {
        console.error('处理事件通知失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ 应用相关IPC处理程序已注册')
  }
}

module.exports = AppIPCHandler
