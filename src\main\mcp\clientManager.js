const fs = require('fs')
const { join } = require('path')
const { app } = require('electron')

// 导入各个 MCP 服务模块
const filesystemMCP = require('./filesystem')
const systemMCP = require('./system')
const wordMCP = require('./word')
const emailMCP = require('./email')
const weatherMCP = require('./weather')
const browserMCP = require('./browser')
const calendarMCP = require('./calendar')

class MCPClientManager {
  constructor() {
    this.clients = new Map()
    this.servers = new Map()
    this.initialized = false
    this.wordMCPProcess = null
    // 用户配置
    this.userConfig = null
  }

  // 获取用户配置
  getUserConfig() {
    if (!this.userConfig) {
      try {
        // 尝试从存储中读取配置
        const configPath = join(app.getPath('userData'), 'config.json')
        if (fs.existsSync(configPath)) {
          const configData = fs.readFileSync(configPath, 'utf8')
          this.userConfig = JSON.parse(configData)
          console.log('📋 已从文件加载用户配置')
        } else {
          console.log('📋 用户配置文件不存在，使用默认配置')
          this.userConfig = {}
        }
      } catch (error) {
        console.error('❌ 读取用户配置失败:', error)
        this.userConfig = {}
      }
    }
    return this.userConfig
  }

  // 保存用户配置
  saveUserConfig(config) {
    try {
      this.userConfig = config
      const configPath = join(app.getPath('userData'), 'config.json')
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf8')
      console.log('📋 用户配置已保存到文件')
      return true
    } catch (error) {
      console.error('❌ 保存用户配置失败:', error)
      return false
    }
  }

  async initialize() {
    if (this.initialized) return

    try {
      console.log('🚀 ====== 开始初始化MCP服务器 ======')
      console.log('🚀 当前时间:', new Date().toISOString())

      // 发送初始化开始状态
      this.sendStatusUpdate('开始初始化MCP服务...')

      // 初始化文件系统MCP服务器
      console.log('🚀 [1/7] 初始化文件系统MCP服务器...')
      this.sendStatusUpdate('正在初始化文件系统服务...')

      try {
        await filesystemMCP.initialize(this)
        console.log('✅ [1/7] 文件系统MCP服务器初始化完成')
        this.sendStatusUpdate('文件系统服务初始化完成')
      } catch (error) {
        console.error('❌ [1/7] 文件系统MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('文件系统服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 初始化系统操作MCP（模拟）
      console.log('🚀 [2/7] 初始化系统操作MCP服务器...')
      this.sendStatusUpdate('正在初始化系统操作服务...')

      try {
        await systemMCP.initialize(this)
        console.log('✅ [2/7] 系统操作MCP服务器初始化完成')
        this.sendStatusUpdate('系统操作服务初始化完成')
      } catch (error) {
        console.error('❌ [2/7] 系统操作MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('系统操作服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // Word MCP服务器设置为按需加载
      this.sendStatusUpdate('配置Word文档服务为按需加载模式')
      console.log('🚀 Word MCP设置为按需加载模式')
      console.log('💡 Word功能将在首次使用时自动初始化')

      // 初始化浏览器MCP服务器
      console.log('🚀 [3/5] 初始化浏览器MCP服务器...')
      this.sendStatusUpdate('正在初始化浏览器控制服务...')

      try {
        await browserMCP.initialize(this)
        console.log('✅ [3/5] 浏览器MCP服务器初始化完成')
        this.sendStatusUpdate('浏览器控制服务初始化完成')
      } catch (error) {
        console.error('❌ [3/5] 浏览器MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('浏览器控制服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // 检查是否有邮件配置，决定是否初始化Email MCP服务器
      const Store = require('electron-store')
      const store = new Store()
      const emailConfig = store.get('emailConfig')
      
      if (emailConfig && emailConfig.user && emailConfig.pass) {
        console.log('🚀 [4/5] 初始化邮件MCP服务器...')
        this.sendStatusUpdate('正在初始化邮件服务...')

        try {
          await emailMCP.initialize(this)
          console.log('✅ [4/5] 邮件MCP服务器初始化完成')
          this.sendStatusUpdate('邮件服务初始化完成')
        } catch (error) {
          console.error('❌ [4/5] 邮件MCP服务器初始化失败:', error.message)
          this.sendStatusUpdate('邮件服务初始化失败，将使用有限功能继续')
          // 继续执行，不要中断整体流程
        }
      } else {
        console.log('⏭️ [4/5] 跳过邮件MCP服务器初始化（未配置或已禁用）')
        this.sendStatusUpdate('跳过邮件服务初始化')
      }

      // 初始化天气MCP服务器
      console.log('🚀 [5/5] 初始化天气MCP服务器...')
      this.sendStatusUpdate('正在初始化天气服务...')

      try {
        await weatherMCP.initialize(this)
        console.log('✅ [5/5] 天气MCP服务器初始化完成')
        this.sendStatusUpdate('天气服务初始化完成')
      } catch (error) {
        console.error('❌ [5/5] 天气MCP服务器初始化失败:', error.message)
        this.sendStatusUpdate('天气服务初始化失败，将使用有限功能继续')
        // 继续执行，不要中断整体流程
      }

      // Outlook日历MCP设置为按需加载
      this.sendStatusUpdate('配置Outlook日历服务为按需加载模式')
      console.log('🚀 Outlook日历MCP设置为按需加载模式')
      console.log('💡 日历功能将在首次使用时自动初始化')

      this.initialized = true
      console.log('🎉 ====== MCP服务器初始化完成 ======')
      this.sendStatusUpdate('MCP服务初始化完成')

    } catch (error) {
      console.error('❌ MCP服务器初始化过程中发生错误:', error)
      this.sendStatusUpdate('MCP服务初始化过程中发生错误')
      throw error
    }
  }

  // 发送状态更新
  sendStatusUpdate(message) {
    // 这里可以通过IPC发送状态更新到渲染进程
    console.log(`📡 状态更新: ${message}`)
  }

  // 获取连接状态
  getConnectionStatus() {
    const status = {}
    
    // 检查各个服务的连接状态
    status.filesystem = filesystemMCP.getConnectionStatus()
    status.system = systemMCP.getConnectionStatus()
    status.word = wordMCP.getConnectionStatus()
    status.browser = browserMCP.getConnectionStatus()
    status.email = emailMCP.getConnectionStatus()
    status.weather = weatherMCP.getConnectionStatus()
    status.calendar = calendarMCP.getConnectionStatus()

    return status
  }

  // 列出可用工具
  async listAvailableTools() {
    const tools = []
    
    // 收集各个服务的可用工具
    tools.push(...filesystemMCP.getAvailableTools())
    tools.push(...systemMCP.getAvailableTools())
    tools.push(...wordMCP.getAvailableTools())
    tools.push(...browserMCP.getAvailableTools())
    tools.push(...emailMCP.getAvailableTools())
    tools.push(...weatherMCP.getAvailableTools())
    tools.push(...calendarMCP.getAvailableTools())

    return tools
  }

  // 调用真实的MCP工具
  async callRealMCPTool(toolName, args) {
    // 根据工具名称分发到对应的服务
    if (toolName.startsWith('filesystem_') || ['search_files', 'list_directory', 'read_file', 'open_file'].includes(toolName)) {
      // 文件系统工具：支持 filesystem_* 前缀和常用别名
      return await filesystemMCP.callTool(toolName, args)
    } else if (toolName.startsWith('system_')) {
      return await systemMCP.callTool(toolName, args)
    } else if (toolName.startsWith('word_')) {
      return await wordMCP.callTool(toolName, args)
    } else if (toolName.startsWith('browser_')) {
      return await browserMCP.callTool(toolName, args)
    } else if (toolName.startsWith('email_') || ['send_email', 'list_email', 'mark_email_as_read'].includes(toolName)) {
      return await emailMCP.callTool(toolName, args)
    } else if (toolName.startsWith('weather_') || ['get_weather_forecast'].includes(toolName)) {
      return await weatherMCP.callTool(toolName, args)
    } else if (toolName.startsWith('calendar_') || ['create_event', 'list_events', 'get_calendars'].includes(toolName)) {
      return await calendarMCP.callTool(toolName, args)
    } else {
      throw new Error(`未知的工具: ${toolName}`)
    }
  }

  // 清理资源
  async cleanup() {
    console.log('🧹 开始清理MCP资源...')
    
    try {
      await filesystemMCP.cleanup()
      await systemMCP.cleanup()
      await wordMCP.cleanup()
      await browserMCP.cleanup()
      await emailMCP.cleanup()
      await weatherMCP.cleanup()
      await calendarMCP.cleanup()
      
      console.log('✅ MCP资源清理完成')
    } catch (error) {
      console.error('❌ MCP资源清理失败:', error)
    }
  }

  // 兼容性方法 - 文件系统操作
  async executeFileSearch(query, directory = null) {
    return await filesystemMCP.executeFileSearch(query, directory)
  }

  async listDirectory(dirPath) {
    return await filesystemMCP.listDirectory(dirPath)
  }

  async readFile(filePath) {
    return await filesystemMCP.readFile(filePath)
  }

  async openFile(filePath, options = {}) {
    return await filesystemMCP.openFile(filePath, options)
  }

  isKnowledgeReferenceFile(filePath) {
    return filesystemMCP.isKnowledgeReferenceFile(filePath)
  }

  // 兼容性方法 - 邮件服务
  async initializeEmailMCP() {
    console.log('📧 初始化邮件 MCP 服务...')
    await emailMCP.initialize(this)
    this.clients.set('email-server', { initialized: true })
    console.log('✅ 邮件 MCP 服务初始化完成')
    return this.clients.get('email-server')
  }

  // 重启邮件MCP服务
  async restartEmailMCP() {
    console.log('📧 重启邮件 MCP 服务...')
    try {
      // 先清理现有的邮件服务
      if (this.clients.has('email-server')) {
        this.clients.delete('email-server')
      }

      // 重置邮件MCP的初始化状态，强制重新初始化
      emailMCP.resetInitialization()

      // 重新初始化邮件服务
      await emailMCP.initialize(this)
      this.clients.set('email-server', { initialized: true })
      console.log('✅ 邮件 MCP 服务重启完成')
      return this.clients.get('email-server')
    } catch (error) {
      console.error('❌ 重启邮件 MCP 服务失败:', error)
      throw error
    }
  }

  // 兼容性方法 - 日历服务
  async initializeOutlookCalendarMCP() {
    console.log('📅 初始化 Outlook 日历 MCP 服务...')
    await calendarMCP.initialize(this)
    // 不要覆盖calendar.js中设置的完整客户端信息
    // this.clients.set('outlook-calendar', { initialized: true })
    console.log('✅ Outlook 日历 MCP 服务初始化完成')
    return this.clients.get('outlook-calendar')
  }

  // 兼容性方法 - 文件系统服务
  async initializeFilesystemMCP() {
    console.log('📁 初始化文件系统 MCP 服务...')
    await filesystemMCP.initialize(this)
    this.clients.set('filesystem', { initialized: true })
    console.log('✅ 文件系统 MCP 服务初始化完成')
    return this.clients.get('filesystem')
  }

  // 兼容性方法 - 浏览器服务
  async restartBrowserMCP() {
    console.log('🌐 重启浏览器 MCP 服务...')
    await browserMCP.cleanup()
    await browserMCP.initialize(this)
    this.clients.set('browser', { initialized: true })
    console.log('✅ 浏览器 MCP 服务重启完成')
    return { success: true, message: '浏览器服务重启成功' }
  }

  // 兼容性方法 - 日历服务重启
  async restartOutlookCalendarMCP() {
    console.log('📅 重启 Outlook 日历 MCP 服务...')
    await calendarMCP.cleanup()
    await calendarMCP.initialize(this)
    this.clients.set('outlook-calendar', { initialized: true })
    console.log('✅ Outlook 日历 MCP 服务重启完成')
    return { success: true, message: '日历服务重启成功' }
  }
}

module.exports = MCPClientManager 