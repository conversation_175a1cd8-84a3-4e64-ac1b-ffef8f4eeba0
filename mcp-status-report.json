{"timestamp": "2025-08-01T03:10:22.667Z", "duration": 0.038, "summary": {"total": 5, "passed": 5, "failed": 0, "passRate": "100.0"}, "results": [{"name": "MCP目录结构检查", "passed": true, "details": "找到3/3个服务目录", "timestamp": "2025-08-01T03:10:22.634Z"}, {"name": "MCP配置文件检查", "passed": true, "details": "配置了4个服务器", "timestamp": "2025-08-01T03:10:22.636Z"}, {"name": "Python环境检查", "passed": true, "details": "Python 3.13.5", "timestamp": "2025-08-01T03:10:22.664Z"}, {"name": "Node.js依赖检查", "passed": true, "details": "所有MCP相关依赖都存在", "timestamp": "2025-08-01T03:10:22.665Z"}, {"name": "端口检查", "passed": true, "details": "使用STDIO通信，无端口冲突", "timestamp": "2025-08-01T03:10:22.665Z"}]}