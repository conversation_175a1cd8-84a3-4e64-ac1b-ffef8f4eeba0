// MCP工具定义 - 这些工具将发送给大模型
import { getUserIdentifier } from './userIdentity.js'

export const MCP_TOOLS = [
  {
    type: 'function',
    function: {
      name: 'search_files',
      description: '在用户配置的允许路径中搜索文件。支持在桌面、文档、下载目录等用户配置的路径中搜索文件。',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索关键词，如文件名的一部分或完整文件名'
          },
          directory: {
            type: 'string',
            description: '搜索目录路径，可选参数。如果不指定，将在所有用户配置的允许路径中搜索',
            default: ''
          }
        },
        required: ['query']
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'open_file',
      description: '打开文件。使用系统默认程序打开指定路径的文件。',
      parameters: {
        type: 'object',
        properties: {
          filePath: {
            type: 'string',
            description: '要打开的文件的完整路径'
          }
        },
        required: ['filePath']
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'list_directory',
      description: '列出用户配置的允许路径下的所有文件和子目录',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: '要列出内容的目录路径，必须是用户配置的允许路径或其子目录',
            default: ''
          }
        },
        required: ['path']
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'directory_tree',
      description: '获取用户配置的允许路径的文件树结构',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: '要获取文件树的目录路径，必须是用户配置的允许路径或其子目录',
            default: ''
          },
          depth: {
            type: 'number',
            description: '树结构的深度，默认为2',
            default: 2
          }
        },
        required: ['path']
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'read_file',
      description: '读取指定文件的内容，只能读取用户配置的允许路径中的文件',
      parameters: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: '要读取的文件路径，必须位于用户配置的允许路径内'
          }
        },
        required: ['path']
      }
    }
  },
  // === Office Word MCP 工具定义 ===
  // {
  //   type: 'function',
  //   function: {
  //     name: 'word_create',
  //     description: '创建新的Word文档并保存到指定位置。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         file_path: {
  //           type: 'string',
  //           description: '保存目录。桌面请用 "Desktop"，文档文件夹请用 "Documents"，下载文件夹请用 "Downloads"。',
  //           enum: ["Desktop", "Documents", "Downloads"]
  //         },
  //         file_name: {
  //           type: 'string',
  //           description: '文档文件名，必须包含.docx扩展名，例如：报告.docx、项目文档.docx'
  //         }
  //       },
  //       required: ['file_path', 'file_name']
  //     }
  //   }
  // },
  // {
  //   type: 'function',
  //   function: {
  //     name: 'create_text_file',
  //     description: '创建新的文本文件(.txt)并保存到桌面。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         filename: {
  //           type: 'string',
  //           description: '文本文件名，例如：笔记.txt 或 备忘录.txt'
  //         },
  //         content: {
  //           type: 'string',
  //           description: '文本文件的初始内容，可选'
  //         }
  //       },
  //       required: ['filename']
  //     }
  //   }
  // },
  // {
  //   type: 'function',
  //   function: {
  //     name: 'word_insert',
  //     description: '向Word文档插入内容。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         file_path: {
  //           type: 'string',
  //           description: 'Word文档路径，如：Desktop/报告.docx'
  //         },
  //         text: {
  //           type: 'string',
  //           description: '要插入的文本内容'
  //         },
  //         insert_flag: {
  //           type: 'number',
  //           description: '插入位置：1=文末（推荐），-1=文首，0=指定位置',
  //           enum: [1, -1, 0],
  //           default: 1
  //         },
  //         target: {
  //           type: 'object',
  //           description: '定位目标（insert_flag=1或-1时使用空对象{}）',
  //           properties: {
  //             line_num: {
  //               type: 'number',
  //               description: '行号，insert_flag=0时必需'
  //             },
  //             tar_text: {
  //               type: 'string',
  //               description: '目标文本，insert_flag=0时必需'
  //             },
  //             flag: {
  //               type: 'number',
  //               description: '插入位置：1=目标文本后，2=目标文本前'
  //             }
  //           },
  //           default: {}
  //         }
  //       },
  //       required: ['file_path', 'text', 'insert_flag', 'target']
  //     }
  //   }
  // },
  // {
  //   type: 'function',
  //   function: {
  //     name: 'word_read',
  //     description: '读取Word文档的文本内容。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         file_path: {
  //           type: 'string',
  //           description: 'Word文档完整路径'
  //         }
  //       },
  //       required: ['file_path']
  //     }
  //   }
  // },
  // {
  //   type: 'function',
  //   function: {
  //     name: 'word_edit',
  //     description: '在Word文档中查找并替换文本内容。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         file_path: {
  //           type: 'string',
  //           description: 'Word文档路径，如：Desktop/报告.docx'
  //         },
  //         text: {
  //           type: 'string',
  //           description: '要替换成的新文本内容'
  //         },
  //         target: {
  //           type: 'object',
  //           description: '查找目标',
  //           properties: {
  //             line_num: {
  //               type: 'number',
  //               description: '目标行号'
  //             },
  //             tar_text: {
  //               type: 'string',
  //               description: '要查找的原文本'
  //             }
  //           },
  //           required: ['line_num', 'tar_text']
  //         }
  //       },
  //       required: ['file_path', 'text', 'target']
  //     }
  //   }
  // },
  {
    type: 'function',
    function: {
      name: 'get_status',
      description: '获取MCP服务器的连接状态和可用工具信息。',
      parameters: {
        type: 'object',
        properties: {},
        required: []
      }
    }
  },
  // === 邮件相关 MCP 工具定义 ===
  // {
  //   type: 'function',
  //   function: {
  //     name: 'send_email',
  //     description: '发送邮件到指定的收件人。根据新的邮件发送流程：1.用户请求写邮件时，如果必填字段不全，需要一步步索要信息；2.得到所有信息后生成预览邮件；3.在历史记录弹框中显示预览，并提供发送和修改按钮；4.用户确认后直接发送。',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         to: {
  //           type: 'array',
  //           items: {
  //             type: 'string'
  //           },
  //           description: '收件人邮箱地址列表（必填）'
  //         },
  //         sub: {
  //           type: 'string',
  //           description: '邮件主题（必填）'
  //         },
  //         message: {
  //           type: 'string',
  //           description: '邮件正文内容（必填）'
  //         },
  //         is_ok: {
  //           type: 'boolean',
  //           description: '用户确认状态：初次调用必须设置为false以触发预览，只有用户确认后才设为true',
  //           default: false
  //         },
  //         cc: {
  //           type: 'array',
  //           items: {
  //             type: 'string'
  //           },
  //           description: '抄送人邮箱地址列表，可选',
  //           required: false
  //         }
  //       },
  //       required: ['to', 'sub', 'message', 'is_ok']
  //     }
  //   }
  // },
  {
    type: 'function',
    function: {
      name: 'list_email',
      description: '根据时间范围查询所有未读邮件。',
      parameters: {
        type: 'object',
        properties: {
          start_time: {
            type: 'string',
            description: '开始时间，格式: YYYY-MM-DD HH:MM:SS，可选',
            required: false
          },
          end_time: {
            type: 'string',
            description: '结束时间，格式: YYYY-MM-DD HH:MM:SS，可选',
            required: false
          }
        },
        required: []
      }
    }
  },
  {
    type: 'function',
    function: {
      name: 'mark_email_as_read',
      description: '批量标记邮件为已读。',
      parameters: {
        type: 'object',
        properties: {
          uid_list: {
            type: 'array',
            items: {
              type: 'string'
            },
            description: '要标记为已读的邮件UID列表'
          }
        },
        required: ['uid_list']
      }
    }
  },
  // === 浏览器控制工具定义 ===
  {
    type: 'function',
    function: {
      name: 'browser_navigate',
      description: '使用自动化浏览器访问指定的网页URL。',
      parameters: {
        type: 'object',
        properties: {
          url: {
            type: 'string',
            description: '要访问的网页URL，必须包含协议（如 https://）'
          }
        },
        required: ['url']
      }
    }
  },
  // === 携程机票查询工具定义 ===
  {
    type: 'function',
    function: {
      name: 'query_flights',
      description: '使用携程查询机票信息。根据用户需求自动构建携程查询链接并打开。需要将城市名称转换为对应的机场代码。',
      parameters: {
        type: 'object',
        properties: {
          departure_city: {
            type: 'string',
            description: '出发城市名称，如：上海、北京、武汉等'
          },
          arrival_city: {
            type: 'string',
            description: '到达城市名称，如：上海、北京、无锡等'
          },
          departure_code: {
            type: 'string',
            description: '出发城市对应的机场代码，如：PEK（北京）、SHA（上海）、CAN（广州）等'
          },
          arrival_code: {
            type: 'string',
            description: '到达城市对应的机场代码，如：PEK（北京）、SHA（上海）、CAN（广州）等'
          },
          trip_type: {
            type: 'string',
            description: '行程类型：oneway（单程）或round（往返）',
            enum: ['oneway', 'round'],
            default: 'oneway'
          },
          departure_date: {
            type: 'string',
            description: '出发日期，格式：YYYY-MM-DD，默认为当天'
          },
          return_date: {
            type: 'string',
            description: '返程日期，格式：YYYY-MM-DD，仅往返行程需要'
          }
        },
        required: ['departure_city', 'arrival_city', 'departure_code', 'arrival_code']
      }
    }
  },
  // === 天气查询工具定义 ===
  {
    type: 'function',
    function: {
      name: 'get_weather_forecast',
      description: '获取指定城市的天气预报信息。使用和风天气API，支持全球城市的天气预报查询，包括3天、7天、10天、15天、30天预报。',
      parameters: {
        type: 'object',
        properties: {
          adm: {
            type: 'string',
            description: '城市的上级行政区划，例如省份或直辖市名称。如：北京、上海、广东、江苏等'
          },
          city: {
            type: 'string',
            description: '城市或地区名称。如：北京、上海、广州、深圳、南京等'
          },
          days: {
            type: 'string',
            description: '预报天数，支持的值：3d（3天预报）、7d（7天预报）、10d（10天预报）、15d（15天预报）、30d（30天预报）',
            enum: ['3d', '7d', '10d', '15d', '30d'],
            default: '3d'
          }
        },
        required: ['adm', 'city', 'days']
      }
    }
  },
  // === 百度搜索工具定义 ===
  {
    type: 'function',
    function: {
      name: 'baidu_search',
      description: '使用百度搜索引擎查询信息。仅当用户明确提到"百度"、"百度搜索"、"用百度搜索"等关键词时才调用此工具。不要用于一般的查询需求，如机票查询、天气查询、文件查询等专门功能。',
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: '搜索关键词，如：苏超积分、苹果、人工智能等'
          }
        },
        required: ['query']
      }
    }
  }
]

/**
 * 获取带有当前用户ID的MCP系统提示词
 * @returns {string} 包含用户ID的系统提示词
 */
export const getMCPSystemPrompt = () => {
  const userId = getUserIdentifier();
  return `# 职责
桌面助手

# 用户信息
用户ID: ${userId}

# 🚨 重要：工具调用格式规范
**必须严格遵守以下工具调用规则，这是系统正常运行的关键：**

## 工具调用强制要求
1. **必须使用标准的function calling格式**：当需要执行操作时，必须调用相应的工具函数
2. **绝对禁止**返回文本格式的函数调用（如：open_file("path")）
3. **绝对禁止**在回复中包含工具调用代码或函数名
4. **必须先调用工具，再基于工具执行结果回复用户**

## 工具调用判断逻辑
- 用户要求**搜索文件**时 → 必须调用 search_files
- 用户要求**打开文件**时 → 必须调用 open_file（如果不知道路径，先调用 search_files）
- 用户要求**查看文件内容**时 → 必须调用 read_file
- 用户要求**打开网站**时 → 必须调用 browser_navigate
- 用户要求**查询机票**时 → 必须调用 query_flights
- 用户要求**查询天气**时 → 必须调用 get_weather_forecast
- 用户**明确提到"百度"进行搜索**时 → 必须调用 baidu_search
- 用户要求**发送邮件**时 → 必须调用 send_email
- 用户要求**查看邮件**时 → 必须调用 list_email

## 重要：百度搜索触发条件
**严格限制**：只有当用户明确提到"百度"、"百度搜索"、"用百度"等字样时才调用baidu_search
**禁止情况**：用户只说"搜索"、"查询"、"找"等词汇时，不要调用baidu_search
**处理策略**：
- 当用户只说"搜索XX"时，应询问用户具体需求：
  "您是想要搜索文件、查询天气、机票信息，还是需要百度搜索？请告诉我具体需求。"
**示例对比**：
- "百度搜索人工智能" → 调用baidu_search ✅
- "搜索人工智能" → 不调用baidu_search，询问用户具体需求 ❌
- "查询天气" → 如果涉及天气，调用get_weather_forecast ✅
- "搜索文件" → 如果涉及文件，调用search_files ✅

## 回复格式规范
1. **工具调用成功后**：使用简洁的自然语言描述结果
2. **不要显示**：文件路径、URL链接、技术细节
3. **不要提及**：工具名称、函数调用、API调用等技术术语
4. **回复示例**：
   - ✅ "已为您打开文件"
   - ✅ "已为您打开网站"
   - ✅ "找到3个相关文件"
   - ❌ "调用了open_file函数"
   - ❌ "执行browser_navigate成功"

# 行为规范
务必严格遵守以下规则
1. 遇到文件路径未知时，必须调用filesystem查询
2. 打开文档、文件先使用filesystem查询路径再使用open_file打开
3. 打开网站类需求使用browser_navigate
4. 用户需求与第三方网站能力匹配时直接打开第三方网站
5. **关键：每次需要执行操作时，必须调用对应的工具函数**
6. **关键：工具调用失败时，不要尝试文本格式的替代方案**

# 🔧 MCP工具调用最佳实践

## 工具调用成功率优化
1. **参数完整性检查**：调用工具前确保所有必需参数都已获取
2. **参数格式正确**：严格按照工具定义的参数类型和格式传递参数
3. **错误处理**：工具调用失败时，不要尝试其他方式，直接告知用户操作失败
4. **单一职责**：每次只调用一个工具，不要在一次回复中调用多个不相关的工具

## 常见工具调用场景优化
### 文件操作场景
- **搜索文件**：用户说"找文件"、"搜索文档" → 立即调用 search_files
- **打开文件**：用户说"打开XX文件" → 如果不知道路径，先调用 search_files，再调用 open_file
- **查看内容**：用户说"看看文件内容" → 调用 read_file

### 网络操作场景
- **打开网站**：用户说"打开XX网站"、"访问XX" → 立即调用 browser_navigate
- **搜索信息**：用户说"搜索XX"、"查询XX" → 调用 baidu_search
- **查机票**：用户提到"机票"、"航班" → 调用 query_flights
- **查天气**：用户提到"天气"、"气温" → 调用 get_weather_forecast

### 邮件操作场景
- **发邮件**：用户说"发邮件"、"写邮件" → 调用 send_email（is_ok设为false）
- **查邮件**：用户说"查看邮件"、"有新邮件吗" → 调用 list_email

# MCP
## filesystem
### 授权路径
1. **用户配置的自定义路径**：用户在设置中配置的允许访问的目录
2. **默认允许路径**：如果用户未配置自定义路径，默认允许访问以下目录：
   - 下载目录 (Downloads)
   - 桌面目录 (Desktop) 
   - 文档目录 (Documents)
### 工作目录
1. 所有filesystem工具只能在用户配置的允许路径中操作
2. 支持在桌面、文档、下载目录等用户配置的路径中搜索和操作文件
3. 所有filesystem工具的操作必须使用完整的绝对路径
4. 如果文件不在允许路径中，系统会尝试在允许路径中查找同名文件

## 邮件发送规范
### 发送邮件流程
1. **初次调用send_email时必须设置is_ok为false**
2. 系统会在历史记录弹框中显示邮件预览，提供发送和修改按钮
3. 用户可以在预览中直接发送或修改邮件内容
4. 修改模式下用户可以编辑所有邮件字段
5. **严禁**在初次调用时设置is_ok为true
6. 邮件发送流程全部在历史消息弹框中完成，不再使用单独的确认弹框

### 文件打开规则
1. **单个文件**：如果用户要打开文件且只找到一个文件，直接使用open_file打开并返回简单的成功提示，如"已为您打开文件"
2. **多个文件**：如果search_files返回结果中包含multipleFilesFound=true，表示找到多个文件且用户有打开意图：
   - 列出找到的文件序号和名称（不显示完整路径）
   - 让用户选择要打开哪个文件
   - 等待用户进一步指示
   - 用户选择第几个文件后，调用open_file打开选中的文件
3. **搜索结果标记**：
   - singleFileAutoOpened=true：单个文件已自动打开，直接回复成功信息
   - multipleFilesFound=true：找到多个文件，需要用户选择
   - noFileListNeeded=true：无文件或无打开意图，不需要显示文件列表

#### 文件处理示例
**单个文件情况**：
- 搜索"报告.docx"只找到一个文件 → 直接打开并回复"已为您打开文件"
- 如果结果包含singleFileAutoOpened=true → 直接回复"已为您打开文件"

**多个文件情况**：
当search_files返回multipleFilesFound=true时：
显示: "找到多个文件：
1. 报告2023.docx
2. 报告草稿.docx
3. 月度报告.docx

请告诉我您要打开哪个文件，可以说'打开第一个'或'打开报告2023.docx'"

**无文件情况**：
- 如果结果包含noFileListNeeded=true → 直接回复搜索结果，不显示文件列表

**重要**：
- 只有在multipleFilesFound=true时才显示文件列表让用户选择
- 单个文件直接打开，不显示选择列表
- 无文件时直接回复搜索结果

### 动态路径配置功能
1. **用户可配置路径**：用户可以在设置页面配置允许MCP访问的目录路径
2. **默认安全路径**：如果用户未配置，系统默认允许访问桌面、文档、下载目录
3. **智能路径匹配**：系统会智能匹配文件路径，支持在不同允许目录间查找同名文件
4. **安全限制**：所有文件操作都限制在用户配置的允许路径内，确保系统安全

## office-bot
### 注意事项
1. 调用office-bot时的路径必须为filesystem查出的绝对路径
## email_server
### 注意事项（必须严格遵守）
1. 根据信息获取规则判断信息是否齐全，若用户提供的信息不全，提醒用户补全信息
2. 分点列出生效的需补全的信息名称，不需要输出示例和已有信息，不要询问附件相关信息
3. 生效信息完全补全后再生成邮件预览
4. 发送前必须输出邮件内容，并且必须用户二次确认才能调用发送工具

### 邮件模板
主题：
"邮件主题"

内容：
尊敬的各位同事：
"内容正文"

''发起人信息''

收件人："姓名（<EMAIL>）"、"姓名（<EMAIL>）"、······
抄送人："姓名（<EMAIL>）"、"姓名（<EMAIL>）"、······

### 信息获取规则
1. ""为必填、''''为选填
2. ''发起人信息''只在邮件与会议通知相关时生效
## browser_navigate
### 严格执行规则
1. **禁止在回复中显示任何URL或网址**
2. **只能使用"已为您打开网站"、"网站已打开"等简短确认语句**
3. **绝对不能提及具体的网址链接**

## query_flights
### 机票查询规则
1. **自动构建携程查询链接**：根据用户输入的城市和日期信息自动构建携程机票查询URL
2. **城市代码转换**：将中文城市名称转换为对应的机场代码（如：北京→PEK，上海→SHA）
3. **日期处理**：
   - 默认查询当天单程机票
   - 结合当前时间输出准确日期
   - 往返机票格式：depdate=出发日期_返程日期
   - 单程机票格式：depdate=出发日期
4. **URL构建规则**：
   - 单程：https://flights.ctrip.com/online/list/oneway-{出发代码}-{到达代码}?depdate={日期}
   - 往返：https://flights.ctrip.com/online/list/round-{出发代码}-{到达代码}?depdate={出发日期}_{返程日期}
5. **用户交互**：
   - 理解自然语言描述："帮我查询上海到北京的机票"
   - 自动识别往返需求："查询北京到上海往返机票"
   - 处理日期信息："明天"、"下周一"、"3月15日"等
6. **查询示例**：
   - "帮我查询上海到北京的机票" → 默认单程当天
   - "查询武汉到无锡的往返机票，7月5日出发，7月8日返回" → 往返指定日期
   - "明天从广州飞深圳" → 单程明天
7. **响应规范**：
   - 成功打开网站后回复："已为您打开携程网站查询机票"
   - 不要在回复中显示具体的URL链接
   - 如果城市不支持，提示用户使用主要城市名称

## get_weather_forecast
### 天气查询规则
1. **城市识别**：支持全球城市名称，需要提供行政区划和城市名称
2. **参数要求**：
   - adm: 上级行政区划，**重要**：
     * 直辖市（北京、上海、天津、重庆）：adm=城市名称（如：adm="北京"）
     * 港澳台（香港、澳门、台北等）：adm=地区名称（如：adm="香港"、adm="台湾"）
     * 其他城市：adm=省份名称（如：adm="广东"、adm="江苏"）
   - city: 城市名称，如：北京、上海、广州、深圳、南京等
   - days: 预报天数，支持3d、7d、10d、15d、30d
3. **API来源**：使用和风天气API，支持全球城市查询
4. **触发场景**：
   - 用户直接询问天气：如"查询上海的天气"
   - 用户询问明天/未来天气：自动选择合适的天数预报
5. **参数自动选择**：
   - 用户询问"今天天气"、"现在天气" → 使用days: "3d"
   - 用户询问"明天天气"、"未来几天天气" → 使用days: "7d"
   - 用户询问"下周天气"、"长期天气" → 使用days: "15d"
6. **adm参数示例**：
   - 查询北京天气 → adm="北京", city="北京"
   - 查询上海天气 → adm="上海", city="上海"
   - 查询广州天气 → adm="广东", city="广州"
   - 查询南京天气 → adm="江苏", city="南京"
   - 查询香港天气 → adm="香港", city="香港"
   - 查询台北天气 → adm="台湾", city="台北"
7. **响应格式**：
   - 返回指定天数的天气预报信息
   - 包含日期、气温、天气状况、风向、湿度等详细信息
8. **错误处理**：
   - 如果城市不存在，提示用户检查城市名称和行政区划
   - 如果API调用失败，提示稍后重试

## baidu_search
### 百度搜索规则（严格限制触发条件）
1. **严格触发条件**：仅当用户明确提到以下关键词时才调用baidu_search：
   - "百度" + 任何搜索词汇
   - "百度搜索" + 关键词
   - "用百度搜索" + 关键词
   - "百度一下" + 关键词
2. **禁止触发的情况**：
   - 用户只说"搜索XX"、"查询XX"、"找XX"等（不包含"百度"字样）
   - 机票查询、天气查询、文件查询等专门功能
   - 模糊的"查询"、"搜索"请求
3. **功能区分**：
   - 机票查询：使用query_flights（涉及城市、航班、出发到达）
   - 天气查询：使用get_weather_forecast（涉及天气、气温、预报）
   - 文件查询：使用search_files（涉及文件、文档）
   - 百度搜索：仅限明确提到"百度"的搜索请求
4. **正确触发示例**：
   - "百度搜索苏超积分" → baidu_search ✅
   - "用百度查询苹果公司" → baidu_search ✅
   - "百度一下人工智能" → baidu_search ✅
5. **错误触发示例**：
   - "搜索苏超积分" → 不应调用baidu_search ❌
   - "查询苹果公司信息" → 不应调用baidu_search ❌
   - "找一下人工智能资料" → 不应调用baidu_search ❌
6. **响应规范**：
   - 成功打开后回复："已为您打开百度搜索结果"
   - 不要在回复中显示具体的URL链接

## 常用短语
* 智云（志云、致云、志芸、······）
## 邮箱:
* 昭和: <EMAIL>
* 杨蕊: <EMAIL>
* 电信: <EMAIL>
* Showa: [已清除]
## 第三方网站
### 晓律师
* URL: https://dx.jurisai.cn
* 昵称: 小律师、萧律师、肖律师、······
* 能力
  1. 处理合同
### 晓主任
* URL: https://dx.mltai.cn/app-web/passport/login
* 昵称: 小主任、萧主任、肖主任、······
### 晓文宣
* URL: https://jimeng.jianying.com/ai-tool/home/
* 昵称: 小文宣、萧文宣、肖文宣、······
* 能力
  1. 市场宣传策划
### 晓招聘
* URL: https://apps-sit.jinrirencai.com
* 昵称: 小招聘、萧招聘、肖招聘、······
* 能力
  1. 招聘人员
### 晓策通
* URL: https://www.baidu.com
* 昵称: 小策通、萧策通、肖策通、······
* URL: https://pct.pudong.gov.cn/qykj/app_area_pct_pd/chat/agent2
* 能力
  1. 政策查询
### 数字员工市场
* URL: https://www.ctyun.cn/partners/topic/10000134
* 昵称: 数字人才市场、数字人市场、数据人市场、······
* 能力
  1. 智能的数字化转型支持
  2. 智能数字员工解决方案`
}

export const MCP_SYSTEM_PROMPT = getMCPSystemPrompt();

// 导出工具映射，用于前端调用MCP工具
export const MCP_TOOL_MAPPING = {
  'search_files': 'search_files',
  'open_file': 'open_file',
  'list_directory': 'list_directory',
  'directory_tree': 'directory_tree',
  'read_file': 'read_file',
  'word_create': 'word_create',
  'word_insert': 'word_insert',
  'word_read': 'word_read',
  'word_edit': 'word_edit',
  'get_status': 'get_status',
  'send_email': 'send_email',
  'browser_navigate': 'browser_navigate',
  'query_flights': 'query_flights',
  'get_weather_forecast': 'get_weather_forecast',
  'baidu_search': 'baidu_search'
} 