# AI CogniDesk Client - main.js重构后全量测试文档

## 测试概述
本文档用于对重构后的main.js进行全量手动测试，确保所有功能模块正常工作。

## 测试环境准备
- [ ] 确保Node.js版本 >= 18.0.0
- [ ] 确保所有依赖已安装 (`npm install`)
- [ ] 确保Python环境已配置（用于MCP服务）
- [ ] 准备测试用的邮箱账号（用于邮件服务测试）
- [ ] 准备测试用的Outlook账号（用于日历服务测试）

## 1. 应用启动与初始化测试

### 1.1 基础启动测试
- [ ] **开发模式启动**: `npm run electron:dev`
  - [ ] 应用正常启动，无崩溃
  - [ ] 主窗口正常显示
  - [ ] 控制台无严重错误信息
  - [ ] 版本检查组件正常显示

- [ ] **生产模式启动**: `npm run electron:dist` 后运行打包文件
  - [ ] 应用正常启动
  - [ ] 主窗口正常显示
  - [ ] 无开发者工具

### 1.2 单实例检查测试
- [ ] **单实例锁测试**
  - [ ] 启动第一个应用实例
  - [ ] 尝试启动第二个实例
  - [ ] 验证第二个实例自动退出
  - [ ] 第一个实例窗口被激活并显示

### 1.3 协议处理测试
- [ ] **协议注册测试**
  - [ ] 在浏览器中访问 `ai-cognidesk://auth/sso/callback?code=test123`
  - [ ] 验证应用被唤醒
  - [ ] 验证授权码被正确传递到渲染进程
  - [ ] 验证防重复发送机制工作正常

## 2. 窗口管理测试

### 2.1 主窗口测试
- [ ] **窗口创建**
  - [ ] 主窗口正常创建并显示
  - [ ] 窗口尺寸符合预期
  - [ ] 窗口位置居中显示

- [ ] **缩放适配测试**
  - [ ] 在100%缩放下测试窗口显示
  - [ ] 在125%缩放下测试窗口显示
  - [ ] 在150%缩放下测试窗口显示
  - [ ] 在175%缩放下测试窗口显示
  - [ ] 验证反向缩放正确应用

- [ ] **窗口操作**
  - [ ] 最小化窗口
  - [ ] 恢复窗口
  - [ ] 关闭窗口（应最小化到托盘）
  - [ ] 从托盘恢复窗口

### 2.2 悬浮窗测试
- [ ] **悬浮窗创建**
  - [ ] 登录后悬浮窗正常创建
  - [ ] 悬浮窗位置正确
  - [ ] 悬浮窗始终置顶

- [ ] **悬浮窗交互**
  - [ ] 悬浮窗可以正常拖拽
  - [ ] 悬浮窗内容正常显示
  - [ ] 悬浮窗与主窗口消息同步

### 2.3 托盘功能测试
- [ ] **托盘图标**
  - [ ] 托盘图标正常显示
  - [ ] 托盘图标右键菜单正常
  - [ ] 托盘通知功能正常

## 3. 用户认证与登录测试

### 3.1 登录流程测试
- [ ] **初始状态**
  - [ ] 应用启动时显示登录界面
  - [ ] 未登录状态下功能受限

- [ ] **登录测试**
  - [ ] 用户名密码登录
  - [ ] SSO登录（如果支持）
  - [ ] Token验证机制
  - [ ] 登录状态持久化

- [ ] **登录后状态**
  - [ ] 主界面正常显示
  - [ ] 用户信息正确显示
  - [ ] 服务初始化开始

### 3.2 Token管理测试
- [ ] **Token获取**
  - [ ] 从主进程存储获取token
  - [ ] 从渲染进程获取token
  - [ ] Token同步机制

- [ ] **Token验证**
  - [ ] 有效token验证
  - [ ] 无效token处理
  - [ ] Token过期处理

## 4. 服务初始化测试

### 4.1 知识库服务测试
- [ ] **知识库初始化**
  - [ ] 知识库模块正常初始化
  - [ ] getCurrentUserToken函数正确设置
  - [ ] 初始化失败时的重试机制

- [ ] **知识库功能**
  - [ ] 知识库查询功能
  - [ ] 知识库管理功能
  - [ ] 知识库同步功能

### 4.2 MCP服务测试
- [ ] **MCP管理器初始化**
  - [ ] MCPClientManager正常初始化
  - [ ] 各个MCP客户端正常连接
  - [ ] MCP服务状态正确显示

- [ ] **各MCP服务测试**
  - [ ] 浏览器MCP服务
  - [ ] 天气MCP服务
  - [ ] 文件系统MCP服务
  - [ ] Word文档MCP服务
  - [ ] 系统MCP服务

### 4.3 邮件服务测试
- [ ] **邮件服务初始化**
  - [ ] EmailService正常创建
  - [ ] 邮件配置检查
  - [ ] 邮件服务状态显示

- [ ] **邮件功能测试**
  - [ ] 邮件配置设置
  - [ ] 邮件接收功能
  - [ ] 邮件发送功能
  - [ ] 邮件通知功能

### 4.4 日历服务测试
- [ ] **日历服务初始化**
  - [ ] OutlookCalendarService正常创建
  - [ ] 日历服务配置
  - [ ] 日历服务状态显示

- [ ] **日历功能测试**
  - [ ] 日历事件查询
  - [ ] 日历事件创建
  - [ ] 日历事件更新
  - [ ] 日历同步功能

## 5. IPC通信测试

### 5.1 IPC管理器测试
- [ ] **IPC注册**
  - [ ] 所有IPC处理程序正确注册
  - [ ] IPC管理器正常工作
  - [ ] IPC消息路由正确

- [ ] **主要IPC通道测试**
  - [ ] 用户认证相关IPC
  - [ ] 服务管理相关IPC
  - [ ] 窗口管理相关IPC
  - [ ] 文件操作相关IPC

## 6. 控制台转发测试

### 6.1 控制台转发功能
- [ ] **控制台转发设置**
  - [ ] setupConsoleForwarding正常执行
  - [ ] 原始console方法保存正确
  - [ ] 转发机制正常工作

- [ ] **转发测试**
  - [ ] console.log消息正确转发
  - [ ] console.error消息正确转发
  - [ ] console.warn消息正确转发
  - [ ] 对象序列化正确处理

## 7. 权限与安全测试

### 7.1 Session权限测试
- [ ] **媒体权限**
  - [ ] 麦克风权限正确授予
  - [ ] 摄像头权限正确授予
  - [ ] 通知权限正确授予

- [ ] **网络权限**
  - [ ] AI服务API访问正常
  - [ ] CORS头正确设置
  - [ ] WebSocket连接正常

## 8. 子进程管理测试

### 8.1 Windows子进程隐藏
- [ ] **spawn拦截**
  - [ ] spawn调用正确拦截
  - [ ] 窗口隐藏选项正确设置
  - [ ] CREATE_NO_WINDOW标志正确应用

- [ ] **子进程测试**
  - [ ] Python子进程正常启动
  - [ ] 子进程窗口不可见
  - [ ] 子进程输出正确捕获

## 9. 错误处理与恢复测试

### 9.1 异常处理测试
- [ ] **服务初始化失败**
  - [ ] 知识库初始化失败处理
  - [ ] MCP服务连接失败处理
  - [ ] 邮件服务初始化失败处理

- [ ] **运行时错误**
  - [ ] 网络连接错误处理
  - [ ] 文件操作错误处理
  - [ ] 权限错误处理

### 9.2 恢复机制测试
- [ ] **自动重试**
  - [ ] 服务连接重试
  - [ ] 网络请求重试
  - [ ] 错误恢复机制

## 10. 性能与资源测试

### 10.1 内存使用测试
- [ ] **内存泄漏检查**
  - [ ] 长时间运行内存稳定
  - [ ] 服务重启后内存释放
  - [ ] 大量操作后内存正常

### 10.2 CPU使用测试
- [ ] **CPU占用**
  - [ ] 空闲状态CPU占用低
  - [ ] 高负载操作CPU合理
  - [ ] 后台运行CPU占用低

## 11. 应用退出与清理测试

### 11.1 正常退出测试
- [ ] **退出流程**
  - [ ] 应用正常退出
  - [ ] 所有服务正确清理
  - [ ] 资源正确释放

- [ ] **清理验证**
  - [ ] 邮件服务清理
  - [ ] 日历服务清理
  - [ ] MCP连接清理
  - [ ] 托盘图标清理

### 11.2 异常退出测试
- [ ] **强制退出**
  - [ ] 强制关闭应用
  - [ ] 下次启动正常
  - [ ] 数据完整性保持

## 测试结果记录

### 通过的测试项
- [ ] 记录所有通过的测试项

### 失败的测试项
- [ ] 记录失败的测试项及详细错误信息

### 需要修复的问题
- [ ] 列出发现的问题和建议的修复方案

## 12. 集成功能测试

### 12.1 语音识别测试
- [ ] **Sherpa-ONNX集成**
  - [ ] Web Audio API正常工作
  - [ ] WASM模块正确加载
  - [ ] 语音识别功能正常
  - [ ] 语音转文字准确性

### 12.2 AI服务集成测试
- [ ] **API客户端测试**
  - [ ] createMainApiClient正常工作
  - [ ] handleMainApiError错误处理
  - [ ] callMainAIService服务调用
  - [ ] API配置正确加载

### 12.3 数据存储测试
- [ ] **electron-store测试**
  - [ ] 配置数据正确存储
  - [ ] 用户数据持久化
  - [ ] 存储数据加密（如适用）
  - [ ] 数据迁移兼容性

## 13. 用户界面测试

### 13.1 主界面功能测试
- [ ] **导航测试**
  - [ ] 各页面切换正常
  - [ ] 页面状态保持
  - [ ] 路由功能正常

- [ ] **组件交互测试**
  - [ ] 按钮点击响应
  - [ ] 表单输入验证
  - [ ] 数据展示正确
  - [ ] 加载状态显示

### 13.2 主题与样式测试
- [ ] **主题切换**
  - [ ] 明暗主题切换
  - [ ] 主题设置持久化
  - [ ] 样式正确应用

- [ ] **响应式设计**
  - [ ] 不同窗口尺寸适配
  - [ ] 缩放比例适配
  - [ ] 字体大小适配

## 14. 网络连接测试

### 14.1 网络状态测试
- [ ] **连接状态检测**
  - [ ] 在线状态检测
  - [ ] 离线状态处理
  - [ ] 网络恢复处理

- [ ] **API连接测试**
  - [ ] 各AI服务API连接
  - [ ] 超时处理机制
  - [ ] 重连机制测试

### 14.2 代理与防火墙测试
- [ ] **代理环境测试**
  - [ ] 企业代理环境
  - [ ] 代理认证处理
  - [ ] 代理配置检测

## 15. 多语言与本地化测试

### 15.1 语言支持测试
- [ ] **中文支持**
  - [ ] 简体中文显示
  - [ ] 中文输入处理
  - [ ] 中文字符编码

- [ ] **英文支持**
  - [ ] 英文界面切换
  - [ ] 英文输入处理
  - [ ] 混合语言处理

## 16. 安全性测试

### 16.1 数据安全测试
- [ ] **敏感数据保护**
  - [ ] Token安全存储
  - [ ] 密码加密存储
  - [ ] 通信数据加密

- [ ] **权限控制测试**
  - [ ] 文件访问权限
  - [ ] 网络访问权限
  - [ ] 系统资源访问

### 16.2 输入验证测试
- [ ] **输入安全**
  - [ ] SQL注入防护
  - [ ] XSS攻击防护
  - [ ] 文件路径验证

## 17. 兼容性测试

### 17.1 操作系统兼容性
- [ ] **Windows版本测试**
  - [ ] Windows 10兼容性
  - [ ] Windows 11兼容性
  - [ ] 不同版本特性支持

### 17.2 硬件兼容性
- [ ] **硬件要求测试**
  - [ ] 最低配置运行
  - [ ] 推荐配置运行
  - [ ] 高配置优化

## 18. 压力测试

### 18.1 负载测试
- [ ] **高并发测试**
  - [ ] 多个MCP服务同时运行
  - [ ] 大量数据处理
  - [ ] 长时间连续运行

### 18.2 边界测试
- [ ] **极限条件测试**
  - [ ] 内存不足情况
  - [ ] 磁盘空间不足
  - [ ] 网络带宽限制

## 19. 日志与调试测试

### 19.1 日志记录测试
- [ ] **日志功能**
  - [ ] 日志正确记录
  - [ ] 日志级别控制
  - [ ] 日志文件轮转

- [ ] **调试功能**
  - [ ] 开发者工具访问
  - [ ] 调试信息输出
  - [ ] 错误堆栈跟踪

## 20. 更新与维护测试

### 20.1 自动更新测试
- [ ] **更新检查**
  - [ ] 版本检查功能
  - [ ] 更新提示显示
  - [ ] 更新下载安装

### 20.2 配置迁移测试
- [ ] **版本升级**
  - [ ] 配置文件迁移
  - [ ] 数据库结构升级
  - [ ] 向后兼容性

## 已修复的问题

### 重构过程中发现并修复的问题
1. **知识库目录选择功能错误** ✅ 已修复
   - 问题：`Cannot read properties of undefined (reading '0')`
   - 原因：IPC返回格式变更，客户端代码未适配
   - 修复：更新`knowledgeClient.js`中的`selectDocumentDirectory`函数，兼容新旧格式

2. **知识库搜索功能错误** ✅ 已修复
   - 问题：`searchResults.filter is not a function`
   - 原因：IPC返回`{success: true, results: []}`格式，但客户端直接当作数组处理
   - 修复：更新`searchKnowledge`函数，正确处理响应格式

3. **邮件配置持久化问题** ✅ 已修复
   - 问题：邮箱账号和授权码保存后重启应用丢失
   - 原因：`get-email-config`返回格式问题，客户端解析错误
   - 修复：更新`EmailConfig.vue`中的`loadConfig`函数

4. **待办事项刷新逻辑验证** ✅ 已验证
   - 现象：没有邮箱配置时仍可刷新待办事项
   - 结论：这是正确的设计，用户应能查看已存储的待办事项

5. **知识库清空功能错误** ✅ 已修复
   - 问题：`knowledge.clearKnowledge is not a function`
   - 原因：IPC处理程序调用了不存在的`clearKnowledge`函数，实际函数名为`clearKnowledgeBase`
   - 修复：更新`src/main/ipc/knowledge.js`中的函数调用

6. **MCP工具调用路由错误** ✅ 已修复
   - 问题：`未知的工具类型: browser_navigate` 和 `ReferenceError: toolType is not defined`
   - 原因：IPC处理程序期望工具类型但传入的是完整工具名称；错误处理中引用了已删除的变量
   - 修复：简化IPC处理程序，直接使用`mcpManager.callRealMCPTool()`方法；修复错误处理中的变量引用

## 测试执行指南

### 自动化测试工具
1. **基础功能检查**: `node test-main-refactor.js`
2. **MCP服务状态检查**: `node mcp-status-checker.js`
3. **一键测试脚本**: `run-tests.bat`

### 测试前准备
1. 备份当前配置和数据
2. 准备测试环境和测试数据
3. 确保网络连接稳定
4. 关闭其他可能干扰的应用
5. 运行自动化检查工具确认基础环境

### 测试执行顺序
1. **第一步：自动化检查**
   ```bash
   # 运行完整的自动化检查
   run-tests.bat
   # 选择选项1进行基础检查
   # 选择选项3进行MCP状态检查
   ```

2. **第二步：基础功能测试**（1-6章节）
   - 应用启动与初始化
   - 窗口管理
   - 用户认证与登录
   - 服务初始化
   - IPC通信
   - 控制台转发

3. **第三步：服务集成测试**（7-12章节）
   - 权限与安全
   - 子进程管理
   - 错误处理与恢复
   - 性能与资源
   - 应用退出与清理
   - 集成功能

4. **第四步：高级功能测试**（13-20章节）
   - 用户界面
   - 网络连接
   - 多语言与本地化
   - 安全性
   - 兼容性
   - 压力测试
   - 日志与调试
   - 更新与维护

### 问题记录格式
```
问题编号: T001
测试项目: [具体测试项目]
问题描述: [详细描述问题现象]
重现步骤: [列出重现步骤]
预期结果: [描述预期的正确结果]
实际结果: [描述实际观察到的结果]
严重程度: [高/中/低]
影响范围: [描述影响的功能范围]
建议修复: [提出修复建议]
修复状态: [待修复/修复中/已修复/已验证]
```

## 测试完成确认
- [ ] 所有核心功能测试通过
- [ ] 所有已知问题已记录
- [ ] 性能指标符合要求
- [ ] 安全性检查通过
- [ ] 兼容性测试通过
- [ ] 应用可以正常发布使用

## 测试报告模板

### 测试摘要
- 测试项目总数: ___
- 通过项目数: ___
- 失败项目数: ___
- 跳过项目数: ___
- 测试通过率: ___%

### 主要发现
1. [列出主要问题]
2. [列出改进建议]
3. [列出优秀表现]

### 风险评估
- 高风险问题: ___个
- 中风险问题: ___个
- 低风险问题: ___个

### 发布建议
- [ ] 建议立即发布
- [ ] 建议修复关键问题后发布
- [ ] 建议延期发布

---
**测试人员**: ___________
**测试日期**: ___________
**测试版本**: v1.0.1-beta
**测试环境**: Windows 10/11
**测试时长**: _____ 小时
