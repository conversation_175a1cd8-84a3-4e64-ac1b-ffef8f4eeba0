{"timestamp": "2025-08-01T03:10:06.683Z", "duration": 0.01, "summary": {"total": 6, "passed": 6, "failed": 0, "passRate": "100.0"}, "results": [{"name": "文件结构检查", "passed": true, "details": "所有必需文件都存在", "timestamp": "2025-08-01T03:10:06.677Z"}, {"name": "main.js语法检查", "passed": true, "details": "所有关键方法都存在", "timestamp": "2025-08-01T03:10:06.678Z"}, {"name": "依赖检查", "passed": true, "details": "所有必需依赖都存在", "timestamp": "2025-08-01T03:10:06.679Z"}, {"name": "配置文件检查", "passed": true, "details": "配置文件检查完成", "timestamp": "2025-08-01T03:10:06.680Z"}, {"name": "MCP服务检查", "passed": true, "details": "找到3个MCP服务", "timestamp": "2025-08-01T03:10:06.681Z"}, {"name": "启动流程模拟", "passed": true, "details": "基础启动检查通过", "timestamp": "2025-08-01T03:10:06.681Z"}]}