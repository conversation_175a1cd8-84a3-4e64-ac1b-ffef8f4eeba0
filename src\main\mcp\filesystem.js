const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')
const { app } = require('electron')
const os = require('os')

let mcpClient = null
let mcpProcess = null
let initialized = false
let realMCPAvailable = false

/**
 * 初始化文件系统 MCP 服务
 */
async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('📁 初始化文件系统 MCP 服务...')

    // 尝试启动真实的MCP服务器
    await initializeRealMCPServer(mcpManager)

    initialized = true
    console.log('✅ 文件系统 MCP 服务初始化完成')
  } catch (error) {
    console.error('❌ 文件系统 MCP 服务初始化失败:', error)
    // 不抛出错误，使用降级方案
    initialized = true
    realMCPAvailable = false
    console.log('⚠️ 使用文件系统MCP降级方案')
  }
}

/**
 * 初始化真实的MCP服务器
 */
async function initializeRealMCPServer(mcpManager) {
  try {
    console.log('🚀 启动真实的文件系统MCP服务器...')

    // 获取用户配置的路径
    const userConfig = mcpManager.getUserConfig()
    let allowedDirs = []

    // 检查用户是否配置了自定义路径
    if (userConfig && userConfig.filePaths && userConfig.filePaths.customPaths &&
      Array.isArray(userConfig.filePaths.customPaths) &&
      userConfig.filePaths.customPaths.length > 0) {
      console.log('🔧 发现用户配置的自定义路径:', userConfig.filePaths.customPaths)
      allowedDirs = [...userConfig.filePaths.customPaths]
    }

    // 如果没有自定义路径或不允许自定义，则使用默认下载目录
    if (allowedDirs.length === 0 ||
      (userConfig && userConfig.filePaths && userConfig.filePaths.allowCustomPaths === false)) {
      const downloadsDir = path.join(os.homedir(), 'Downloads')
      allowedDirs = [downloadsDir]
      console.log('🔒 MCP文件系统使用默认设置：仅允许下载目录访问')
    } else {
      console.log('🔓 MCP文件系统使用自定义设置：允许访问指定目录')
    }

    console.log('📁 允许访问的目录:', allowedDirs)

    // 动态导入MCP SDK
    let StdioClientTransport, Client
    try {
      console.log('📦 动态加载MCP SDK...')
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      StdioClientTransport = stdioModule.StdioClientTransport

      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      Client = clientModule.Client

      console.log('✅ MCP SDK加载成功')
    } catch (importError) {
      console.error('❌ MCP SDK加载失败:', importError)
      throw new Error('MCP SDK不可用')
    }

    // 使用 NPX 启动 filesystem MCP 服务器
    const npxCommand = process.platform === 'win32' ? 'npx.cmd' : 'npx'
    const args = [
      '-y',
      '@modelcontextprotocol/server-filesystem',
      ...allowedDirs
    ]

    console.log('🚀 启动Filesystem MCP服务器:')
    console.log('  - 命令:', npxCommand)
    console.log('  - 参数:', args)

    // 创建MCP客户端连接
    const transport = new StdioClientTransport({
      command: npxCommand,
      args: args,
      env: process.env
    })

    const client = new Client({
      name: 'nezha-filesystem-client',
      version: '1.0.0'
    }, {
      capabilities: {}
    })

    await client.connect(transport)
    console.log('✅ 文件系统MCP客户端连接成功')

    // 获取可用工具
    const tools = await client.listTools()
    console.log('📋 文件系统MCP可用工具:', tools.tools?.map(t => t.name) || [])

    mcpClient = client
    mcpProcess = transport
    realMCPAvailable = true

    console.log('✅ 真实文件系统MCP服务器初始化完成')
  } catch (error) {
    console.error('❌ 真实MCP服务器初始化失败:', error)
    throw error
  }
}

/**
 * 获取用户配置的搜索路径
 */
function getUserSearchPaths() {
  const { app } = require('electron')
  const os = require('os')

  try {
    // 尝试从存储中读取配置
    const configPath = path.join(app.getPath('userData'), 'config.json')
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8')
      const userConfig = JSON.parse(configData)

      // 检查用户是否配置了自定义路径
      if (userConfig && userConfig.filePaths && userConfig.filePaths.customPaths &&
        Array.isArray(userConfig.filePaths.customPaths) &&
        userConfig.filePaths.customPaths.length > 0) {
        console.log('🔧 使用用户配置的自定义路径:', userConfig.filePaths.customPaths)
        return userConfig.filePaths.customPaths
      }
    }
  } catch (error) {
    console.warn('⚠️ 读取用户配置失败，使用默认路径:', error.message)
  }

  // 使用默认路径
  const homedir = os.homedir()
  const defaultPaths = [
    path.join(homedir, 'Downloads'),
    path.join(homedir, 'Desktop'),
    path.join(homedir, 'Documents')
  ]

  console.log('🔒 使用默认搜索路径:', defaultPaths)
  return defaultPaths
}

/**
 * 执行文件搜索
 */
async function executeFileSearch(query, directory = null) {
  try {
    console.log(`🔍 执行文件搜索: "${query}"${directory ? ` 在目录: ${directory}` : ''}`)

    // 如果真实MCP可用，使用真实MCP
    if (realMCPAvailable && mcpClient) {
      return await executeRealMCPSearch(query, directory)
    } else {
      // 降级到本地搜索
      return await executeLocalSearch(query, directory)
    }
  } catch (error) {
    console.error('❌ 文件搜索失败:', error)
    return {
      success: false,
      error: error.message,
      files: [],
      query: query
    }
  }
}

/**
 * 使用真实MCP执行搜索
 */
async function executeRealMCPSearch(query, directory = null) {
  try {
    console.log('🔍 使用真实MCP客户端进行文件搜索')

    // 获取搜索路径
    const searchPaths = getUserSearchPaths()
    let searchPath = searchPaths[0] // 默认使用第一个路径

    if (directory) {
      // 验证目录是否在允许范围内
      const isAllowed = searchPaths.some(allowedPath => {
        const resolvedDirectory = path.resolve(directory)
        const resolvedAllowed = path.resolve(allowedPath)
        return resolvedDirectory.startsWith(resolvedAllowed)
      })

      if (isAllowed) {
        searchPath = directory
      } else {
        console.warn(`⚠️ 指定目录不在允许范围内: ${directory}，使用默认路径`)
      }
    }

    console.log(`🔍 使用MCP搜索路径: ${searchPath}`)

    // 增强查询处理：添加智能后缀猜测
    const originalQuery = query
    let enhancedQueries = [query] // 原始查询始终是第一优先级

    // 如果查询不包含文件扩展名，添加常见扩展名
    if (!query.includes('.')) {
      const commonExtensions = ['.docx', '.pdf', '.txt', '.xlsx', '.pptx', '.doc', '.xls', '.ppt']
      enhancedQueries = enhancedQueries.concat(
        commonExtensions.map(ext => query + ext)
      )
    }

    console.log(`🔍 增强查询列表:`, enhancedQueries)

    let foundResults = false
    let allResults = []

    for (const enhancedQuery of enhancedQueries) {
      console.log(`🔍 尝试查询: "${enhancedQuery}"`)

      try {
        // 调用MCP的search_files工具
        const searchResult = await mcpClient.callTool({
          name: 'search_files',
          arguments: {
            query: enhancedQuery,
            path: searchPath,
            fuzzy_match: true // 启用模糊匹配
          }
        })

        console.log(`🔍 MCP搜索结果:`, searchResult)

        if (searchResult && searchResult.content && searchResult.content.length > 0) {
          try {
            const resultText = searchResult.content[0].text
            const resultData = JSON.parse(resultText)

            if (resultData.files && Array.isArray(resultData.files) && resultData.files.length > 0) {
              console.log(`✅ 找到 ${resultData.files.length} 个文件 (查询: "${enhancedQuery}")`)
              allResults = allResults.concat(resultData.files.map(file => ({
                name: path.basename(file.path || file.name || ''),
                path: file.path || file.name || '',
                size: file.size || 0,
                modified: file.modified || file.mtime || new Date(),
                directory: path.dirname(file.path || file.name || ''),
                query: enhancedQuery
              })))
              foundResults = true

              // 如果是原始查询找到结果，优先使用
              if (enhancedQuery === originalQuery) {
                break
              }
            }
          } catch (parseError) {
            console.warn(`⚠️ 解析MCP搜索结果失败 (查询: "${enhancedQuery}"):`, parseError)
          }
        }
      } catch (mcpError) {
        console.warn(`⚠️ MCP搜索失败 (查询: "${enhancedQuery}"):`, mcpError.message)
      }

      // 如果找到结果，不再尝试其他查询
      if (foundResults) break
    }

    // 去重并排序
    const uniqueResults = []
    const seenPaths = new Set()

    for (const result of allResults) {
      if (!seenPaths.has(result.path)) {
        seenPaths.add(result.path)
        uniqueResults.push(result)
      }
    }

    // 按文件名相关性排序
    uniqueResults.sort((a, b) => {
      const aScore = a.name.toLowerCase().indexOf(originalQuery.toLowerCase())
      const bScore = b.name.toLowerCase().indexOf(originalQuery.toLowerCase())
      if (aScore !== bScore) {
        return aScore - bScore
      }
      return a.name.localeCompare(b.name)
    })

    console.log(`✅ 真实MCP搜索完成，找到 ${uniqueResults.length} 个文件`)
    if (uniqueResults.length > 0) {
      console.log(`📁 找到的文件:`)
      uniqueResults.slice(0, 5).forEach((file, index) => {
        console.log(`  ${index + 1}. "${file.name}" -> "${file.path}"`)
      })
      if (uniqueResults.length > 5) {
        console.log(`  ... 还有 ${uniqueResults.length - 5} 个文件`)
      }
    }

    return {
      success: true,
      files: uniqueResults,
      searchPaths: [searchPath],
      query: originalQuery,
      usedRealMCP: true
    }
  } catch (error) {
    console.error('❌ 真实MCP搜索失败:', error)
    // 降级到本地搜索
    console.log('🔄 降级到本地搜索...')
    return await executeLocalSearch(query, directory)
  }
}

/**
 * 执行本地搜索（降级方案）
 */
async function executeLocalSearch(query, directory = null) {
  try {
    console.log('🔍 使用本地搜索（降级方案）')

    // 获取搜索路径
    let searchDirs = []
    if (directory) {
      // 如果指定了目录，验证是否在允许的路径内
      const allowedPaths = getUserSearchPaths()
      const isAllowed = allowedPaths.some(allowedPath => {
        const resolvedDirectory = path.resolve(directory)
        const resolvedAllowed = path.resolve(allowedPath)
        return resolvedDirectory.startsWith(resolvedAllowed)
      })

      if (isAllowed) {
        searchDirs = [directory]
      } else {
        console.warn(`⚠️ 指定目录不在允许范围内: ${directory}`)
        searchDirs = getUserSearchPaths()
      }
    } else {
      searchDirs = getUserSearchPaths()
    }

    console.log(`🔍 本地搜索目录列表:`, searchDirs)

    const results = []

    // 使用 Node.js 的 fs 模块进行文件搜索
    function searchRecursive(dir, query, maxDepth = 3, currentDepth = 0) {
      try {
        if (currentDepth > maxDepth) return

        const items = fs.readdirSync(dir)

        for (const item of items) {
          const fullPath = path.join(dir, item)

          try {
            const stat = fs.statSync(fullPath)

            if (stat.isDirectory() && currentDepth < maxDepth) {
              // 递归搜索子目录，但限制深度
              searchRecursive(fullPath, query, maxDepth, currentDepth + 1)
            } else if (stat.isFile()) {
              // 检查文件名是否匹配查询
              if (item.toLowerCase().includes(query.toLowerCase())) {
                results.push({
                  name: item,
                  path: fullPath,
                  size: stat.size,
                  modified: stat.mtime,
                  directory: path.dirname(fullPath)
                })
              }
            }
          } catch (statError) {
            // 忽略无法访问的文件/目录
            console.debug(`跳过无法访问的项目: ${fullPath}`)
          }
        }
      } catch (error) {
        console.warn(`⚠️ 搜索目录失败: ${dir}`, error.message)
      }
    }

    // 在所有允许的目录中搜索
    for (const searchDir of searchDirs) {
      if (fs.existsSync(searchDir)) {
        console.log(`🔍 本地搜索目录: ${searchDir}`)
        searchRecursive(searchDir, query)
      } else {
        console.warn(`⚠️ 搜索目录不存在: ${searchDir}`)
      }
    }

    // 按文件名相关性排序
    results.sort((a, b) => {
      const aScore = a.name.toLowerCase().indexOf(query.toLowerCase())
      const bScore = b.name.toLowerCase().indexOf(query.toLowerCase())
      if (aScore !== bScore) {
        return aScore - bScore
      }
      return a.name.localeCompare(b.name)
    })

    console.log(`✅ 本地搜索完成，找到 ${results.length} 个文件`)
    if (results.length > 0) {
      console.log(`📁 找到的文件:`)
      results.slice(0, 5).forEach((file, index) => {
        console.log(`  ${index + 1}. "${file.name}" -> "${file.path}"`)
      })
      if (results.length > 5) {
        console.log(`  ... 还有 ${results.length - 5} 个文件`)
      }
    }

    return {
      success: true,
      files: results,
      searchPaths: searchDirs,
      query: query,
      usedRealMCP: false
    }
  } catch (error) {
    console.error('❌ 本地搜索失败:', error)
    return {
      success: false,
      error: error.message,
      files: [],
      query: query,
      usedRealMCP: false
    }
  }
}

/**
 * 列出目录内容
 */
async function listDirectory(dirPath) {
  try {
    console.log(`📂 列出目录: ${dirPath}`)
    
    if (!fs.existsSync(dirPath)) {
      throw new Error(`目录不存在: ${dirPath}`)
    }
    
    const items = fs.readdirSync(dirPath)
    const results = []
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item)
      const stat = fs.statSync(fullPath)
      
      results.push({
        name: item,
        path: fullPath,
        type: stat.isDirectory() ? 'directory' : 'file',
        size: stat.size,
        modified: stat.mtime
      })
    }
    
    console.log(`✅ 目录列表完成，共 ${results.length} 个项目`)
    return {
      success: true,
      items: results
    }
  } catch (error) {
    console.error('❌ 列出目录失败:', error)
    return {
      success: false,
      error: error.message,
      items: []
    }
  }
}

/**
 * 读取文件内容
 */
async function readFile(filePath) {
  try {
    console.log(`📖 读取文件: ${filePath}`)
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }
    
    const stat = fs.statSync(filePath)
    if (stat.size > 10 * 1024 * 1024) { // 10MB
      throw new Error('文件过大，无法读取')
    }
    
    const content = fs.readFileSync(filePath, 'utf8')
    
    console.log(`✅ 文件读取完成，大小: ${content.length} 字符`)
    return {
      success: true,
      content,
      size: content.length,
      modified: stat.mtime
    }
  } catch (error) {
    console.error('❌ 读取文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 打开文件
 */
async function openFile(filePath, options = {}) {
  try {
    console.log(`📂 打开文件: ${filePath}`)
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }
    
    // 使用系统默认程序打开文件
    const { platform } = require('os')
    const currentPlatform = platform()
    
    let command
    let args
    
    switch (currentPlatform) {
      case 'win32':
        command = 'cmd'
        args = ['/c', 'start', '', filePath]
        break
      case 'darwin':
        command = 'open'
        args = [filePath]
        break
      default: // linux
        command = 'xdg-open'
        args = [filePath]
        break
    }
    
    const child = spawn(command, args, {
      detached: true,
      stdio: 'ignore'
    })
    
    child.unref()
    
    console.log(`✅ 文件已使用系统默认程序打开`)
    return {
      success: true,
      message: '文件已打开'
    }
  } catch (error) {
    console.error('❌ 打开文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 检查是否为知识库引用文件
 */
function isKnowledgeReferenceFile(filePath) {
  const knowledgeExtensions = ['.txt', '.md', '.docx', '.doc', '.pdf']
  const ext = path.extname(filePath).toLowerCase()
  return knowledgeExtensions.includes(ext)
}

/**
 * 获取连接状态
 */
function getConnectionStatus() {
  return {
    connected: initialized,
    service: 'filesystem'
  }
}

/**
 * 获取可用工具
 */
function getAvailableTools() {
  return [
    {
      name: 'filesystem_search',
      description: '搜索文件',
      parameters: {
        query: { type: 'string', description: '搜索关键词' },
        directory: { type: 'string', description: '搜索目录（可选）' }
      }
    },
    {
      name: 'filesystem_list',
      description: '列出目录内容',
      parameters: {
        path: { type: 'string', description: '目录路径' }
      }
    },
    {
      name: 'filesystem_read',
      description: '读取文件内容',
      parameters: {
        path: { type: 'string', description: '文件路径' }
      }
    },
    {
      name: 'filesystem_open',
      description: '打开文件',
      parameters: {
        path: { type: 'string', description: '文件路径' }
      }
    }
  ]
}

/**
 * 调用工具
 */
async function callTool(toolName, args) {
  console.log(`🔧 文件系统工具调用: ${toolName}`, args)

  switch (toolName) {
    case 'filesystem_search':
    case 'search_files':  // 别名支持
      // 支持多种参数格式
      const query = args.query || args.search || ''
      const directory = args.directory || args.path || args.dir || null
      console.log(`🔍 搜索参数: query="${query}", directory="${directory || '默认'}"`)
      return await executeFileSearch(query, directory)

    case 'filesystem_list':
    case 'list_directory':  // 别名支持
      return await listDirectory(args.path || args.directory)

    case 'filesystem_read':
    case 'read_file':  // 别名支持
      return await readFile(args.path || args.file)

    case 'filesystem_open':
    case 'open_file':  // 别名支持
      return await openFile(args.path || args.file, args.options)

    default:
      throw new Error(`未知的文件系统工具: ${toolName}`)
  }
}

/**
 * 清理资源
 */
async function cleanup() {
  console.log('🧹 清理文件系统 MCP 资源...')
  initialized = false
}

module.exports = {
  initialize,
  executeFileSearch,
  listDirectory,
  readFile,
  openFile,
  isKnowledgeReferenceFile,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
} 