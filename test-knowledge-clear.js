/**
 * 测试知识库清空功能
 * 用于验证修复后的clearKnowledgeBase函数是否正常工作
 */

const { app, BrowserWindow, ipcMain } = require('electron')
const path = require('path')

// 模拟测试环境
async function testKnowledgeClear() {
  console.log('🧪 开始测试知识库清空功能...')
  
  try {
    // 导入知识库模块
    const knowledge = require('./src/main/knowledge')
    
    console.log('📋 检查知识库模块导出...')
    console.log('可用函数:', Object.keys(knowledge))
    
    // 检查clearKnowledgeBase函数是否存在
    if (typeof knowledge.clearKnowledgeBase === 'function') {
      console.log('✅ clearKnowledgeBase函数存在')
    } else {
      console.log('❌ clearKnowledgeBase函数不存在')
      return false
    }
    
    // 检查其他相关函数
    const requiredFunctions = [
      'initializeKnowledge',
      'searchKnowledge',
      'getKnowledgeStats',
      'indexKnowledgeDocument'
    ]
    
    for (const func of requiredFunctions) {
      if (typeof knowledge[func] === 'function') {
        console.log(`✅ ${func}函数存在`)
      } else {
        console.log(`❌ ${func}函数不存在`)
      }
    }
    
    console.log('🎉 知识库模块函数检查完成')
    return true
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    return false
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testKnowledgeClear().then(success => {
    console.log(success ? '✅ 测试通过' : '❌ 测试失败')
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('测试运行失败:', error)
    process.exit(1)
  })
}

module.exports = { testKnowledgeClear }
