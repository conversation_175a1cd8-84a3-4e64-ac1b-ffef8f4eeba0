const { ipcMain } = require('electron')
const Store = require('electron-store')

// === 邮件服务管理器 ===
class EmailService {
  constructor(mcpManager, mainWindow) {
    this.mcpManager = mcpManager
    this.mainWindow = mainWindow
    this.isInitialized = false
    this.pollingInterval = null
    this.todoList = []
    this.reminders = new Map() // 存储提醒定时器
    
    // 添加持久化存储
    this.todoStore = new Store({
      name: 'email-todos',
      defaults: {
        todos: [],
        reminders: []
      }
    })
    
    // 初始化时加载待办事项
    this.loadTodosFromStore()
    
    console.log('🔧 开始创建EmailService实例...')
    console.log(`📧 [EMAIL_SERVICE] 从存储加载了 ${this.todoList.length} 个待办事项`)
    
    // 恢复提醒设置
    this.restoreReminders()
    console.log(`📧 [EMAIL_SERVICE] 恢复了 ${this.reminders.size} 个提醒`)
    
    console.log('🔧 EmailService实例创建成功，开始初始化...')
  }

  async initialize() {
    console.log('📧 [EMAIL_SERVICE] 开始初始化邮件服务...')

    try {
      // 设置待办事项处理程序
      console.log('📧 [EMAIL_SERVICE] 设置待办事项处理程序...')
      this.setupTodoHandlers()

      // 邮件配置处理程序由main.js统一管理，这里不重复设置
      console.log('📧 [EMAIL_SERVICE] 邮件配置处理程序由main.js统一管理')

      // 检查是否有邮件配置
      const store = new Store()
      let emailConfig = store.get('emailConfig')

      if (emailConfig && emailConfig.user) {
        console.log('📧 [EMAIL_SERVICE] 发现邮件配置，将执行邮件检查')
        console.log('📧 [EMAIL_CONFIG] 当前配置用户:', emailConfig.user)
        console.log('📧 [EMAIL_CONFIG] 如需删除，请在应用界面的邮件配置页面点击"删除配置"')
      }

      if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
        console.log('⚠️ [EMAIL_SERVICE] 未找到邮件配置，邮件服务将以有限功能运行')
        this.isInitialized = true
        return
      }

      // 等待MCP服务初始化完成
      const mcpReady = await this.waitForMCPReady()
      
      if (mcpReady) {
        // 只启动轮询，不立即检查邮件
        // 首次检查将在所有服务都初始化完成后触发
        this.startPolling()
        console.log('📧 [EMAIL_SERVICE] 邮件服务初始化完成（等待所有MCP服务就绪后开始首次检查）')
      } else {
        console.log('⚠️ [EMAIL_SERVICE] 邮件MCP服务未就绪，邮件服务将以有限功能运行')
      }
      
      this.isInitialized = true
      console.log('✅ [EMAIL_SERVICE] 邮件服务初始化完成')
      
    } catch (error) {
      console.error('❌ [EMAIL_SERVICE] 邮件服务初始化失败:', error)
      this.isInitialized = true // 即使失败也标记为已初始化，避免阻塞其他服务
      throw error
    }
  }

  async waitForMCPReady(maxAttempts = 30, intervalMs = 1000) {
    console.log('📧 [EMAIL_SERVICE] 等待邮件MCP服务启动...')
    
    let attempts = 0
    while (attempts < maxAttempts) {
      const emailClient = this.mcpManager.clients.get('email-server')
      if (emailClient && emailClient.isConnected) {
        console.log('📧 [EMAIL_SERVICE] 邮件MCP服务已就绪', emailClient.isRealMCP ? '(真实MCP)' : '(模拟模式)')
        return true
      }
      
      attempts++
      console.log(`📧 [EMAIL_SERVICE] 等待邮件MCP服务... (${attempts}/${maxAttempts})`)
      await new Promise(resolve => setTimeout(resolve, intervalMs))
    }
    
    console.log('⚠️ [EMAIL_SERVICE] 邮件MCP服务启动超时')
    return false
  }

  startPolling() {
    // 启动5分钟轮询
    this.pollingInterval = setInterval(() => {
      this.checkEmails()
    }, 5 * 60 * 1000) // 5分钟
    
    console.log('📧 [EMAIL_SERVICE] 已启动5分钟轮询')
  }

  async performInitialEmailCheck() {
    if (!this.isInitialized) {
      console.log('⚠️ [EMAIL_SERVICE] 服务未初始化，跳过首次邮件检查')
      return
    }

    console.log('📧 [EMAIL_SERVICE] 开始首次邮件检查（查询近24小时）')
    
    // 查询近24小时的邮件
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    
    await this.checkEmails(yesterday, now, true)
  }

  async checkEmails(startTime = null, endTime = null, isInitialCheck = false) {
    try {
      console.log('📧 [EMAIL_SERVICE] 开始检查未读邮件...')
      
      // 如果没有指定时间范围，使用默认逻辑
      if (!startTime || !endTime) {
        if (isInitialCheck) {
          console.log('📧 [EMAIL_SERVICE] 初始化模式：查询近24小时的未读邮件')
          const now = new Date()
          endTime = now
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        } else {
          console.log('📧 [EMAIL_SERVICE] 轮询模式：查询近5分钟的未读邮件')
          const now = new Date()
          endTime = now
          startTime = new Date(now.getTime() - 5 * 60 * 1000)
        }
      }

      // 格式化时间为本地时区
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
      console.log('📧 [EMAIL_SERVICE] 当前时区:', timeZone)
      
      const startTimeStr = this.formatDateForEmail(startTime)
      const endTimeStr = this.formatDateForEmail(endTime)
      
      console.log(`📧 [EMAIL_SERVICE] 查询时间范围：${startTimeStr} 至 ${endTimeStr}`)

      // 调用MCP工具获取邮件
      const result = await this.mcpManager.callRealMCPTool('list_email', {
        start_time: startTimeStr,
        end_time: endTimeStr
      })

      if (!result || !result.content || !Array.isArray(result.content)) {
        console.log('📧 [EMAIL_SERVICE] 未获取到有效的邮件数据')
        return { success: true, message: '未获取到邮件数据', emails: [], todos: [] }
      }

      const emails = result.content
      console.log(`📧 [EMAIL_SERVICE] 获取到 ${emails.length} 封邮件`)

      // 筛选待办类邮件
      const todoEmails = await this.extractTodoEmails(emails)
      console.log(`📧 [EMAIL_SERVICE] 筛选出 ${todoEmails.length} 封待办邮件`)

      // 更新待办列表
      const newTodos = this.updateTodoList(todoEmails)

      // 设置提醒
      this.scheduleReminders(todoEmails)

      // 同步新待办事项到Outlook日历
      if (newTodos.length > 0) {
        await this.syncNewTodosToCalendar(newTodos)
      }

      // 发送通知到渲染进程
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('new-emails-processed', {
          totalEmails: emails.length,
          todoEmails: todoEmails.length,
          todos: this.todoList
        })
      }

      return {
        success: true,
        message: `处理了 ${emails.length} 封邮件，其中 ${todoEmails.length} 封为待办类邮件`,
        emails,
        todos: todoEmails
      }

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 检查邮件时出错:', error)
      return { success: false, error: error.message }
    }
  }

  formatDateForEmail(date) {
    // 转换为本地时间字符串，格式：YYYY-MM-DD HH:MM:SS
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  async extractTodoEmails(emails) {
    const todoKeywords = [
      '待办', '任务', '提醒', '会议', '截止', '完成', '处理', '回复', '确认', '审批',
      'todo', 'task', 'reminder', 'meeting', 'deadline', 'complete', 'process', 'reply', 'confirm', 'approve',
      '请', '需要', '麻烦', '帮忙', '协助', '配合', '支持',
      'please', 'need', 'require', 'help', 'assist', 'support', 'cooperate'
    ]
    
    const urgentKeywords = [
      '紧急', '急', '立即', '马上', '尽快', '今天', '明天',
      'urgent', 'asap', 'immediately', 'today', 'tomorrow', 'quickly'
    ]
    
    return emails.filter(email => {
      const subject = (email.subject || '').toLowerCase()
      const content = (email.content || '').toLowerCase()
      const fullText = subject + ' ' + content
      
      // 检查是否包含待办关键词
      const hasTodoKeyword = todoKeywords.some(keyword => 
        fullText.includes(keyword.toLowerCase())
      )
      
      // 检查是否包含紧急关键词
      const hasUrgentKeyword = urgentKeywords.some(keyword => 
        fullText.includes(keyword.toLowerCase())
      )
      
      return hasTodoKeyword || hasUrgentKeyword
    }).map(email => ({
      ...email,
      isUrgent: urgentKeywords.some(keyword => 
        (email.subject + ' ' + email.content).toLowerCase().includes(keyword.toLowerCase())
      )
    }))
  }

  updateTodoList(newTodoEmails) {
    // 添加新的待办事项，避免重复
    const existingIds = new Set(this.todoList.map(todo => todo.uid))

    const newTodos = newTodoEmails.filter(email => !existingIds.has(email.uid))

    if (newTodos.length > 0) {
      console.log(`📧 [EMAIL_SERVICE] 新增 ${newTodos.length} 个待办事项`)
      this.todoList.push(...newTodos)
    }

    // 保存待办事项到持久化存储
    this.saveTodosToStore()

    console.log(`📧 [EMAIL_SERVICE] 待办列表已更新，当前共 ${this.todoList.length} 项`)

    return newTodos // 返回新添加的待办事项
  }

  /**
   * 同步新增的待办事项到Outlook日历
   */
  async syncNewTodosToCalendar(newTodos) {
    try {
      console.log(`📅 [EMAIL_SERVICE] 开始同步 ${newTodos.length} 个待办事项到Outlook日历`)

      // 获取Outlook日历客户端 - 支持按需初始化
      let outlookClient = this.mcpManager.clients.get('outlook-calendar')

      // 如果客户端不存在，尝试按需初始化
      if (!outlookClient || !outlookClient.isConnected) {
        console.log('📅 [EMAIL_SERVICE] Outlook日历客户端未初始化，正在按需初始化...')
        try {
          outlookClient = await this.mcpManager.initializeOutlookCalendarMCP()
          console.log('✅ [EMAIL_SERVICE] Outlook日历客户端按需初始化成功')
        } catch (initError) {
          console.warn('📅 [EMAIL_SERVICE] Outlook日历MCP按需初始化失败，跳过同步:', initError.message)
          console.warn('📅 [EMAIL_SERVICE] 建议启动Microsoft Outlook后重试')
          return
        }
      }

      if (!outlookClient || !outlookClient.isConnected) {
        console.warn('📅 [EMAIL_SERVICE] Outlook日历MCP客户端仍不可用，跳过同步')
        console.warn('📅 [EMAIL_SERVICE] 客户端状态:', {
          exists: !!outlookClient,
          isConnected: outlookClient?.isConnected,
          configSource: outlookClient?.configSource
        })
        return
      }

      // 逐个同步待办事项
      for (const todo of newTodos) {
        try {
          // 构建日历事件参数
          const eventParams = this.buildCalendarEventParams(todo)

          console.log(`📅 [EMAIL_SERVICE] 创建日历事件: ${todo.subject}`)

          // 调用MCP工具创建日历事件
          const result = await this.mcpManager.callRealMCPTool('create_event', eventParams)

          if (result && result.success) {
            console.log(`✅ [EMAIL_SERVICE] 日历事件创建成功: ${todo.subject}`)

            // 记录同步状态
            todo.syncedToCalendar = true
            todo.calendarEventId = result.eventId || 'unknown'
          } else {
            console.warn(`⚠️ [EMAIL_SERVICE] 日历事件创建失败: ${todo.subject}`, result?.error)
          }

        } catch (syncError) {
          console.error(`❌ [EMAIL_SERVICE] 同步待办事项到日历失败: ${todo.subject}`, syncError)
        }
      }

    } catch (error) {
      console.error('❌ [EMAIL_SERVICE] 同步待办事项到Outlook日历时出错:', error)
    }
  }

  buildCalendarEventParams(todo) {
    const now = new Date()
    let startDateTime, endDateTime

    // 使用解析出的截止时间
    if (todo.dueDate) {
      const dueTime = new Date(todo.dueDate)

      if (todo.todoType === 'meeting') {
        // 会议类型：使用截止时间作为会议开始时间
        startDateTime = dueTime
        endDateTime = new Date(dueTime.getTime() + 60 * 60 * 1000) // 默认1小时会议
      } else {
        // 任务类型：在截止时间前1小时设置提醒事件
        const reminderTime = new Date(dueTime.getTime() - 60 * 60 * 1000)
        startDateTime = reminderTime
        endDateTime = new Date(reminderTime.getTime() + 30 * 60 * 1000) // 30分钟提醒事件
      }
    } else {
      // 没有截止时间，设置为明天的合适时间
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

      if (todo.todoType === 'meeting') {
        // 会议设置为明天上午10点
        tomorrow.setHours(10, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 60 * 60 * 1000) // 1小时会议
      } else {
        // 任务设置为明天上午9点
        tomorrow.setHours(9, 0, 0, 0)
        startDateTime = tomorrow
        endDateTime = new Date(tomorrow.getTime() + 30 * 60 * 1000) // 30分钟提醒
      }
    }

    // 清理发件人字段中的特殊字符
    const cleanFrom = (todo.from || '').replace(/["\r\n\\]/g, '').replace(/=\?[^?]+\?[^?]+\?[^?]+\?=/g, '发件人')
    const cleanBody = (todo.body || todo.todoDescription || '').replace(/["\r\n\\]/g, ' ').substring(0, 200)

    // 构建事件参数
    return {
      subject: `[邮件待办] ${todo.subject}`,
      body: `来自邮件的待办事项\n\n发件人: ${cleanFrom}\n邮件内容: ${cleanBody}...`,
      start: startDateTime.toISOString(),
      end: endDateTime.toISOString(),
      isAllDay: false,
      reminder: (todo.urgency === 'high') ? 15 : 60 // 紧急事项15分钟提醒，普通事项1小时提醒
    }
  }

  scheduleReminders(todoEmails) {
    for (const email of todoEmails) {
      if (email.dueDate && !this.reminders.has(email.uid)) {
        try {
          const dueTime = new Date(email.dueDate)
          const reminderTime = new Date(dueTime.getTime() - 30 * 60 * 1000) // 提前30分钟
          const now = new Date()

          if (reminderTime > now) {
            const delay = reminderTime.getTime() - now.getTime()

            const timerId = setTimeout(() => {
              this.showReminder(email)
              this.reminders.delete(email.uid)
            }, delay)

            this.reminders.set(email.uid, {
              timerId,
              email,
              reminderTime: reminderTime.toISOString(),
              scheduledTime: reminderTime.getTime()
            })

            console.log(`📧 [EMAIL_SERVICE] 已设置提醒: ${email.subject} 将在 ${reminderTime.toLocaleString()} 提醒`)
          }
        } catch (error) {
          console.error('📧 [EMAIL_SERVICE] 设置提醒失败:', error)
        }
      }
    }

    // 保存提醒信息到存储
    this.saveRemindersToStore()
  }

  showReminder(email) {
    console.log(`📧 [EMAIL_SERVICE] 显示提醒: ${email.subject}`)

    // 构建提醒信息
    const reminderData = {
      id: email.uid,
      subject: email.subject,
      from: email.from,
      todoDescription: email.todoDescription,
      urgency: email.urgency,
      dueDate: email.dueDate,
      timestamp: new Date().toISOString()
    }

    // 发送到渲染进程显示提醒弹框
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send('show-email-reminder', reminderData)
    }

    // 系统通知
    const { Notification } = require('electron')
    const path = require('path')
    if (Notification.isSupported()) {
      const notification = new Notification({
        title: '📋 待办事项提醒',
        body: `${email.subject}\n${email.todoDescription}`,
        icon: path.join(__dirname, '../../public/assets/logo.ico'),
        sound: true,
        urgency: email.urgency === 'high' ? 'critical' : 'normal'
      })

      notification.show()

      // 点击通知时显示主窗口待办事项页面
      notification.on('click', () => {
        if (this.mainWindow) {
          this.mainWindow.show()
          this.mainWindow.focus()
          // 切换到待办事项页面
          this.mainWindow.webContents.send('navigate-to', 'todo')
        }
      })
    }

    // 语音播报提醒
    this.speakReminder(email)
  }

  async speakReminder(email) {
    try {
      // 构建语音文本
      let speechText = `犇犇提醒您，`

      if (email.urgency === 'high') {
        speechText += '紧急！'
      }

      speechText += `您有一个${this.getTodoTypeText(email.todoType)}需要处理：${email.subject}。`

      if (email.dueDate) {
        const dueTime = new Date(email.dueDate)
        const now = new Date()
        const timeDiff = dueTime.getTime() - now.getTime()
        const minutesLeft = Math.floor(timeDiff / (1000 * 60))

        if (minutesLeft > 0) {
          if (minutesLeft < 60) {
            speechText += `还有${minutesLeft}分钟到期。`
          } else {
            const hoursLeft = Math.floor(minutesLeft / 60)
            speechText += `还有${hoursLeft}小时到期。`
          }
        } else {
          speechText += '已经到期，请尽快处理。'
        }
      }

      // 发送语音播报请求到渲染进程
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('speak-text', {
          text: speechText,
          volume: 1.0,
          rate: 2.4,
          pitch: 1.0
        })
      }
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 语音播报失败:', error)
    }
  }

  getTodoTypeText(todoType) {
    const typeMap = {
      'meeting': '会议',
      'task': '任务',
      'deadline': '截止日期提醒',
      'reply': '需要回复的事项',
      'approval': '审批事项'
    }
    return typeMap[todoType] || '待办事项'
  }

  loadTodosFromStore() {
    try {
      const storedTodos = this.todoStore.get('todos', [])
      this.todoList = storedTodos
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 加载待办事项失败:', error)
      this.todoList = []
    }
  }

  saveTodosToStore() {
    try {
      this.todoStore.set('todos', this.todoList)
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 保存待办事项失败:', error)
    }
  }

  restoreReminders() {
    console.log('📧 [EMAIL_SERVICE] 恢复提醒设置...')

    // 清除现有的提醒
    this.reminders.forEach(reminder => {
      clearTimeout(reminder.timerId)
    })
    this.reminders.clear()

    // 为未完成的待办事项重新设置提醒
    const now = new Date()
    for (const todo of this.todoList) {
      if (!todo.completed && todo.dueDate) {
        try {
          const dueTime = new Date(todo.dueDate)
          const reminderTime = new Date(dueTime.getTime() - 30 * 60 * 1000) // 提前30分钟

          if (reminderTime > now) {
            const delay = reminderTime.getTime() - now.getTime()

            const timerId = setTimeout(() => {
              this.showReminder(todo)
              this.reminders.delete(todo.uid)
            }, delay)

            this.reminders.set(todo.uid, {
              timerId,
              email: todo,
              reminderTime: reminderTime.toISOString(),
              scheduledTime: reminderTime.getTime()
            })

            console.log(`📧 [EMAIL_SERVICE] 已恢复提醒: ${todo.subject} 将在 ${reminderTime.toLocaleString()} 提醒`)
          }
        } catch (error) {
          console.error('📧 [EMAIL_SERVICE] 恢复提醒失败:', error)
        }
      }
    }

    console.log(`📧 [EMAIL_SERVICE] 恢复了 ${this.reminders.size} 个提醒`)
  }

  saveRemindersToStore() {
    try {
      const remindersArray = Array.from(this.reminders.values()).map(reminder => ({
        email: reminder.email,
        scheduledTime: reminder.scheduledTime
      }))
      this.todoStore.set('reminders', remindersArray)
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 保存提醒失败:', error)
    }
  }

  setupTodoHandlers() {
    console.log('📋 设置待办事项处理程序...')

    // 获取待办事项列表
    ipcMain.handle('get-todo-list', () => {
      return {
        success: true,
        todos: this.todoList
      }
    })

    // 手动检查邮件
    ipcMain.handle('check-emails-manual', async () => {
      try {
        console.log('📧 [EMAIL_SERVICE] 收到手动检查邮件请求')
        const result = await this.checkEmails(false)
        console.log('📧 [EMAIL_SERVICE] 手动检查邮件完成:', result)
        return result
      } catch (error) {
        console.error('❌ 手动检查邮件失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 标记待办事项为完成
    ipcMain.handle('mark-todo-complete', (event, todoId) => {
      const todoIndex = this.todoList.findIndex(todo => todo.uid === todoId)
      if (todoIndex !== -1) {
        this.todoList[todoIndex].completed = true
        this.todoList[todoIndex].completedAt = new Date().toISOString()
        this.saveTodosToStore()
        return { success: true }
      }
      return { success: false, error: '待办事项未找到' }
    })

    // 删除待办事项
    ipcMain.handle('delete-todo', (event, todoId) => {
      const todoIndex = this.todoList.findIndex(todo => todo.uid === todoId)
      if (todoIndex !== -1) {
        this.todoList.splice(todoIndex, 1)
        this.saveTodosToStore()

        // 取消相关提醒
        if (this.reminders.has(todoId)) {
          clearTimeout(this.reminders.get(todoId).timerId)
          this.reminders.delete(todoId)
          this.saveRemindersToStore()
        }

        return { success: true }
      }
      return { success: false, error: '待办事项未找到' }
    })

    // 更新待办事项
    ipcMain.handle('update-todo', (event, todoId, updates) => {
      try {
        const todoIndex = this.todoList.findIndex(todo => todo.uid === todoId)
        if (todoIndex !== -1) {
          // 更新待办事项
          Object.assign(this.todoList[todoIndex], updates)
          this.todoList[todoIndex].updatedAt = new Date().toISOString()

          // 如果更新了截止时间，重新设置提醒
          if (updates.dueDate !== undefined) {
            // 清除旧提醒
            if (this.reminders.has(todoId)) {
              clearTimeout(this.reminders.get(todoId).timerId)
              this.reminders.delete(todoId)
            }

            // 设置新提醒
            if (updates.dueDate) {
              this.scheduleReminders([this.todoList[todoIndex]])
            }
          }

          this.saveTodosToStore()
          this.saveRemindersToStore()

          return { success: true, todo: this.todoList[todoIndex] }
        }
        return { success: false, error: '待办事项未找到' }
      } catch (error) {
        console.error('❌ 更新待办事项失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 清除提醒
    ipcMain.handle('clear-reminder', async (event, reminderId) => {
      try {
        console.log('🔔 收到清除提醒请求:', reminderId)

        // 查找对应的待办事项
        const todo = this.todoList.find(item => item.uid === reminderId)
        if (todo) {
          // 更新提醒状态
          todo.reminderShown = true
          todo.reminderDismissed = true

          // 保存到存储
          this.saveTodosToStore()

          // 清除定时器
          if (this.reminders.has(reminderId)) {
            clearTimeout(this.reminders.get(reminderId).timerId)
            this.reminders.delete(reminderId)
            this.saveRemindersToStore()
          }

          console.log('✅ 提醒已清除:', reminderId)
          return { success: true }
        } else {
          console.warn('⚠️ 未找到对应的提醒:', reminderId)
          return { success: false, error: '未找到对应的提醒' }
        }
      } catch (error) {
        console.error('❌ 清除提醒失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ 待办事项处理程序设置完成')
  }



  async checkEmails(isInitial = false) {
    try {
      // 检查是否有邮件配置
      const Store = require('electron-store')
      const store = new Store()
      const emailConfig = store.get('emailConfig')

      if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
        console.log('⚠️ [EMAIL_SERVICE] 未配置邮箱，跳过邮件检查')
        return { success: true, message: '未配置邮箱，跳过邮件检查', emails: [], todos: [] }
      }

      console.log('📧 [EMAIL_SERVICE] 开始检查未读邮件...')

      // 计算时间范围
      const now = new Date()
      let startTime

      if (isInitial) {
        // 初始化时查询近24小时的邮件
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        console.log('📧 [EMAIL_SERVICE] 初始化模式：查询近24小时的未读邮件')
      } else {
        // 定期轮询时查询近30分钟的邮件
        startTime = new Date(now.getTime() - 30 * 60 * 1000)
        console.log('📧 [EMAIL_SERVICE] 轮询模式：查询近30分钟的未读邮件')
      }

      // 使用本地时间而不是UTC时间
      const formatLocalTime = (date) => {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      const startTimeStr = formatLocalTime(startTime)
      const endTimeStr = formatLocalTime(now)

      // 显示时区信息用于调试
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
      const timezoneOffset = now.getTimezoneOffset() / -60 // 转换为小时，负号是因为getTimezoneOffset返回的是反向的
      console.log(`📧 [EMAIL_SERVICE] 当前时区: ${timezone} (UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset})`)

      console.log(`📧 [EMAIL_SERVICE] 查询时间范围：${startTimeStr} 至 ${endTimeStr}`)

      // 查询指定时间范围的未读邮件
      const emailResult = await this.mcpManager.callRealMCPTool('list_email', {
        start_time: startTimeStr,
        end_time: endTimeStr
      })

      if (!emailResult.success) {
        console.error('📧 [EMAIL_SERVICE] 查询邮件失败:', emailResult.error)
        return { success: false, error: emailResult.error }
      }

      const emails = emailResult.emails || []
      console.log(`📧 [EMAIL_SERVICE] 查询到 ${emails.length} 封未读邮件`)

      if (emails.length === 0) {
        return { success: true, message: '没有新邮件', emails: [], todos: [] }
      }

      // 标记邮件为已读
      const uids = emails.map(email => email.uid).filter(uid => uid)
      if (uids.length > 0) {
        const markResult = await this.mcpManager.callRealMCPTool('mark_email_as_read', { uid_list: uids })
        console.log('📧 [EMAIL_SERVICE] 邮件标记结果:', markResult.success ? '成功' : '失败')
      }

      // 筛选待办类邮件
      const todoEmails = await this.extractTodoEmails(emails)
      console.log(`📧 [EMAIL_SERVICE] 筛选出 ${todoEmails.length} 封待办邮件`)

      // 更新待办列表
      this.updateTodoList(todoEmails)

      // 设置提醒
      this.scheduleReminders(todoEmails)

      // 发送通知到渲染进程
      if (this.mainWindow && this.mainWindow.webContents) {
        this.mainWindow.webContents.send('new-emails-processed', {
          totalEmails: emails.length,
          todoEmails: todoEmails.length,
          todos: this.todoList
        })
      }

      return {
        success: true,
        message: `处理了 ${emails.length} 封邮件，其中 ${todoEmails.length} 封为待办类邮件`,
        emails,
        todos: todoEmails
      }

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 检查邮件时出错:', error)
      return { success: false, error: error.message }
    }
  }

  async extractTodoEmails(emails) {
    try {
      console.log('📧 [EMAIL_SERVICE] 开始AI筛选待办邮件...')

      // 构建邮件摘要
      const emailSummaries = emails.map((email, index) => ({
        id: index + 1,
        subject: email.subject || '无主题',
        from: email.from || '未知发件人',
        preview: (email.body || '').substring(0, 200) + '...',
        date: email.date || '未知时间'
      }))

      // 构建AI请求
      const prompt = `请分析以下邮件，筛选出需要用户执行具体行动的待办类邮件。待办类邮件包括：
1. 会议邀请和安排
2. 任务分配和工作要求
3. 截止日期提醒
4. 需要回复或处理的重要事项
5. 审批、确认类请求

请只返回JSON格式的结果，包含待办邮件的ID和提取的待办事项：

邮件列表：
${emailSummaries.map(email => `${email.id}. 主题：${email.subject}\n发件人：${email.from}\n内容预览：${email.preview}\n时间：${email.date}\n`).join('\n')}

请返回格式：
{
  "todoEmails": [
    {
      "emailId": 1,
      "todoType": "meeting|task|deadline|reply|approval",
      "todoDescription": "具体的待办事项描述",
      "urgency": "high|medium|low",
      "dueDate": "如果能从邮件中提取到截止时间，格式YYYY-MM-DD HH:MM"
    }
  ]}`

      // 使用关键词智能分类邮件
      console.log('📧 [EMAIL_SERVICE] 使用关键词智能分类邮件')

      const todoEmails = emails.map(email => {
        const subject = (email.subject || '').toLowerCase()
        const body = (email.body || '').toLowerCase()
        const content = subject + ' ' + body

        // 分类逻辑
        let todoType = 'task' // 默认任务
        if (content.includes('会议') || content.includes('meeting') || content.includes('开会')) {
          todoType = 'meeting'
        } else if (content.includes('截止') || content.includes('deadline') || content.includes('到期')) {
          todoType = 'deadline'
        } else if (content.includes('回复') || content.includes('reply') || content.includes('回答')) {
          todoType = 'reply'
        } else if (content.includes('审批') || content.includes('approve') || content.includes('确认')) {
          todoType = 'approval'
        }

        // 紧急程度判断
        let urgency = 'medium' // 默认普通
        if (content.includes('紧急') || content.includes('urgent') || content.includes('立即') || content.includes('马上')) {
          urgency = 'high'
        } else if (content.includes('不急') || content.includes('有空') || content.includes('方便时')) {
          urgency = 'low'
        }

        // 解析截止时间
        const dueDate = this.extractDueDate(email.subject, email.body)

        return {
          ...email,
          todoType,
          todoDescription: email.body || email.subject || '邮件待办事项',
          urgency,
          dueDate,
          id: Date.now() + Math.random() // 生成唯一ID
        }
      })

      return todoEmails

    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] AI筛选邮件时出错:', error)
      return []
    }
  }

  extractDueDate(subject, body) {
    try {
      const content = `${subject || ''} ${body || ''}`.toLowerCase()
      const now = new Date()

      // 匹配时间模式
      const timePatterns = [
        // 今天 + 时间：今天16:35、今天下午3点
        /今天\s*(\d{1,2})[:\：](\d{2})/,
        /今天\s*下午\s*(\d{1,2})\s*[点时]/,
        /今天\s*上午\s*(\d{1,2})\s*[点时]/,
        /今天\s*(\d{1,2})\s*[点时]/,

        // 明天 + 时间
        /明天\s*(\d{1,2})[:\：](\d{2})/,
        /明天\s*下午\s*(\d{1,2})\s*[点时]/,
        /明天\s*上午\s*(\d{1,2})\s*[点时]/,
        /明天\s*(\d{1,2})\s*[点时]/,

        // 具体日期 + 时间：8月1日16:35
        /(\d{1,2})月(\d{1,2})日\s*(\d{1,2})[:\：](\d{2})/,
        /(\d{1,2})月(\d{1,2})日\s*(\d{1,2})\s*[点时]/,

        // 直接时间：16:35
        /(\d{1,2})[:\：](\d{2})/
      ]

      for (const pattern of timePatterns) {
        const match = content.match(pattern)
        if (match) {
          let targetDate = new Date(now)
          let hour = 0
          let minute = 0

          if (pattern.source.includes('今天')) {
            // 今天的时间
            if (match[2]) {
              hour = parseInt(match[1])
              minute = parseInt(match[2])
            } else {
              hour = parseInt(match[1])
              // 根据上下文判断上午下午
              if (content.includes('下午') && hour < 12) {
                hour += 12
              }
            }
            targetDate.setHours(hour, minute, 0, 0)
          } else if (pattern.source.includes('明天')) {
            // 明天的时间
            targetDate.setDate(targetDate.getDate() + 1)
            if (match[2]) {
              hour = parseInt(match[1])
              minute = parseInt(match[2])
            } else {
              hour = parseInt(match[1])
              if (content.includes('下午') && hour < 12) {
                hour += 12
              }
            }
            targetDate.setHours(hour, minute, 0, 0)
          } else if (match[4]) {
            // 具体日期：8月1日16:35
            const month = parseInt(match[1])
            const day = parseInt(match[2])
            hour = parseInt(match[3])
            minute = parseInt(match[4])

            targetDate.setMonth(month - 1, day)
            targetDate.setHours(hour, minute, 0, 0)

            // 如果日期已过，设为明年
            if (targetDate < now) {
              targetDate.setFullYear(targetDate.getFullYear() + 1)
            }
          } else {
            // 直接时间，假设是今天
            hour = parseInt(match[1])
            minute = parseInt(match[2]) || 0
            targetDate.setHours(hour, minute, 0, 0)

            // 如果时间已过，设为明天
            if (targetDate < now) {
              targetDate.setDate(targetDate.getDate() + 1)
            }
          }

          console.log(`📧 [EMAIL_SERVICE] 解析到截止时间: ${targetDate.toLocaleString()}`)
          return targetDate.toISOString()
        }
      }

      console.log('📧 [EMAIL_SERVICE] 未能解析到截止时间')
      return null
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 解析截止时间失败:', error)
      return null
    }
  }

  updateTodoList(todoEmails) {
    // 添加新的待办事项
    const newTodos = []
    for (const email of todoEmails) {
      const existingTodo = this.todoList.find(todo => todo.emailId === email.uid)
      if (!existingTodo) {
        const newTodo = {
          id: email.id,
          emailId: email.uid,
          uid: email.uid,
          subject: email.subject,
          from: email.from,
          todoDescription: email.todoDescription,
          todoType: email.todoType,
          urgency: email.urgency,
          dueDate: email.dueDate,
          createdAt: new Date().toISOString(),
          completed: false
        }
        this.todoList.push(newTodo)
        newTodos.push(newTodo)
      }
    }

    console.log(`📧 [EMAIL_SERVICE] 待办列表已更新，当前共 ${this.todoList.length} 项`)

    // 自动同步新增的待办事项到Outlook日历
    if (newTodos.length > 0) {
      console.log(`📧 [EMAIL_SERVICE] 发现 ${newTodos.length} 个新待办事项，准备同步到Outlook日历`)
      this.syncNewTodosToCalendar(newTodos)
    }

    // 保存待办事项到持久化存储
    this.saveTodosToStore()

    return newTodos // 返回新添加的待办事项
  }

  startPolling() {
    // 清除现有的轮询定时器
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }

    // 设置新的轮询定时器（每30分钟检查一次）
    this.pollingInterval = setInterval(async () => {
      try {
        console.log('📧 [EMAIL_SERVICE] 定时检查邮件...')
        await this.checkEmails(false)
      } catch (error) {
        console.error('📧 [EMAIL_SERVICE] 定时检查邮件失败:', error)
      }
    }, 30 * 60 * 1000) // 30分钟

    console.log('📧 [EMAIL_SERVICE] 邮件轮询已启动（每30分钟检查一次）')
  }

  async startInitialEmailCheck() {
    if (!this.isInitialized) {
      console.warn('📧 [EMAIL_SERVICE] 邮件服务未初始化，跳过首次检查')
      return
    }

    // 再次检查邮件配置，确保不会在没有配置的情况下执行检查
    const Store = require('electron-store')
    const store = new Store()
    const emailConfig = store.get('emailConfig')

    if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
      console.log('⚠️ [EMAIL_SERVICE] 未配置邮箱，跳过首次邮件检查')
      return
    }

    console.log('📧 [EMAIL_SERVICE] 开始首次邮件检查（查询近24小时）')
    await this.checkEmails(true)
  }

  /**
   * 保存邮件配置
   */
  async saveEmailConfig(config) {
    try {
      const Store = require('electron-store')
      const store = new Store()

      // 验证必要字段
      if (!config.user || !config.pass) {
        throw new Error('邮箱账号和授权码不能为空')
      }

      // 保存到electron-store
      store.set('emailConfig', config)
      console.log('📧 [EMAIL_CONFIG] 配置已保存到文件')

      // 重启邮件MCP服务以使配置生效
      await this.restartEmailMCP()

      // 启动邮件轮询
      this.startPolling()

      // 立即执行一次邮件检查
      setTimeout(async () => {
        await this.checkEmails(true)
      }, 2000) // 延迟2秒执行，确保MCP服务完全启动

      return { success: true, message: '邮件配置保存成功，邮件服务已启动' }
    } catch (error) {
      console.error('📧 [EMAIL_CONFIG] 保存邮件配置失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 删除邮件配置
   */
  async deleteEmailConfig() {
    try {
      const Store = require('electron-store')
      const store = new Store()

      // 停止邮件轮询
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }

      // 删除邮件配置
      store.delete('emailConfig')
      console.log('📧 [EMAIL_CONFIG] 邮件配置已删除')

      // 删除所有待办事项
      console.log(`📧 [EMAIL_CONFIG] 删除前待办事项数量: ${this.todoList.length}`)
      this.todoList = []
      this.saveTodosToStore()
      console.log(`📧 [EMAIL_CONFIG] 删除后待办事项数量: ${this.todoList.length}`)
      console.log('📧 [EMAIL_CONFIG] 所有待办事项已删除')

      // 清理提醒
      this.reminders.forEach(reminder => {
        if (reminder.timerId) {
          clearTimeout(reminder.timerId)
        }
      })
      this.reminders.clear()

      // 通知前端刷新待办列表
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('email-config-deleted')
      }

      return { success: true, message: '邮件配置已删除，邮件服务已停止' }
    } catch (error) {
      console.error('📧 [EMAIL_CONFIG] 删除邮件配置失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 测试邮件配置
   */
  async testEmailConfig(config) {
    try {
      console.log('📧 [EMAIL_CONFIG] 开始测试邮件配置...')

      // 验证必要字段
      if (!config.user || !config.pass) {
        throw new Error('邮箱账号和授权码不能为空')
      }

      // 临时设置环境变量
      const originalEnv = {}
      const testEnvVars = {
        EMAIL_USER: config.user,
        EMAIL_PASS: config.pass,
        EMAIL_SMTP_SERVER: config.smtpServer || 'smtp.qq.com',
        EMAIL_SMTP_PORT: String(config.smtpPort || 465),
        EMAIL_SMTP_SSL: String(config.smtpSsl !== false),
        EMAIL_IMAP_SERVER: config.imapServer || 'imap.qq.com',
        EMAIL_IMAP_PORT: String(config.imapPort || 993),
        EMAIL_IMAP_SSL: String(config.imapSsl !== false)
      }

      // 保存原始环境变量并设置测试环境变量
      for (const [key, value] of Object.entries(testEnvVars)) {
        originalEnv[key] = process.env[key]
        process.env[key] = value
      }

      // 重启邮件MCP服务进行测试
      await this.restartEmailMCP()

      // 尝试调用邮件MCP服务来测试连接
      const testResult = await this.mcpManager.callRealMCPTool('list_email', {
        start_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
        end_time: new Date().toISOString().slice(0, 19).replace('T', ' ')
      })

      // 恢复原始环境变量
      for (const [key, value] of Object.entries(originalEnv)) {
        if (value === undefined) {
          delete process.env[key]
        } else {
          process.env[key] = value
        }
      }

      if (testResult.success) {
        console.log('📧 [EMAIL_CONFIG] 邮件配置测试成功')
        return { success: true, message: '邮件配置测试成功，连接正常' }
      } else {
        console.error('📧 [EMAIL_CONFIG] 邮件配置测试失败:', testResult.error)
        return { success: false, error: '连接测试失败: ' + testResult.error }
      }
    } catch (error) {
      console.error('📧 [EMAIL_CONFIG] 邮件配置测试失败:', error)
      return { success: false, error: '连接测试失败: ' + error.message }
    }
  }

  /**
   * 重启邮件MCP服务
   */
  async restartEmailMCP() {
    try {
      console.log('📧 [EMAIL_SERVICE] 重启邮件MCP服务...')

      // 通过MCP管理器重启邮件服务
      if (this.mcpManager && this.mcpManager.restartEmailMCP) {
        await this.mcpManager.restartEmailMCP()
      } else {
        console.warn('📧 [EMAIL_SERVICE] MCP管理器不支持重启邮件服务')
      }

      console.log('📧 [EMAIL_SERVICE] 邮件MCP服务重启完成')
    } catch (error) {
      console.error('📧 [EMAIL_SERVICE] 重启邮件MCP服务失败:', error)
      throw error
    }
  }

  cleanup() {
    console.log('🧹 清理邮件服务资源...')

    // 清理轮询定时器
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }

    // 清理所有提醒定时器
    this.reminders.forEach(reminder => {
      clearTimeout(reminder.timerId)
    })
    this.reminders.clear()

    console.log('✅ 邮件服务资源清理完成')
  }
}

module.exports = EmailService
