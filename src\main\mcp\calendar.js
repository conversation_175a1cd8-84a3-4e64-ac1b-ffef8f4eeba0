// Outlook日历 MCP 模块
const fs = require('fs')
const { join } = require('path')

let initialized = false
let mcpClient = null
let mcpTransport = null
let availableTools = []

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('📅 正在初始化Outlook日历MCP服务器...')

    // 读取MCP配置文件
    const mcpConfigPath = isDev
      ? join(process.cwd(), 'mcp-config.json')
      : join(process.resourcesPath, 'mcp-config.json')
    console.log('📋 读取Outlook日历MCP配置文件:', mcpConfigPath)

    if (!fs.existsSync(mcpConfigPath)) {
      console.error('❌ MCP配置文件不存在:', mcpConfigPath)
      throw new Error('MCP配置文件不存在')
    }

    const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'))
    console.log('📋 Outlook日历MCP配置内容:', JSON.stringify(mcpConfig, null, 2))

    // 获取outlook-calendar-mcp配置
    const calendarServerConfig = mcpConfig.mcpServers['outlook-calendar-mcp']
    if (!calendarServerConfig) {
      console.error('❌ 未找到outlook-calendar-mcp配置')
      throw new Error('未找到outlook-calendar-mcp配置')
    }

    console.log('🔧 outlook-calendar-mcp配置:', calendarServerConfig)
    console.log('🔧 命令:', calendarServerConfig.command)
    console.log('🔧 参数:', calendarServerConfig.args)

    // 动态导入MCP SDK
    console.log('📦 动态加载MCP SDK...')
    try {
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      const StdioClientTransport = stdioModule.StdioClientTransport

      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      const Client = clientModule.Client

      console.log('✅ MCP SDK加载成功')

      // 创建传输层 - 让StdioClientTransport自己处理进程启动
      console.log('🚀 创建Outlook日历MCP传输层...')
      console.log('🚀 命令:', calendarServerConfig.command)
      console.log('🚀 参数:', calendarServerConfig.args)

      mcpTransport = new StdioClientTransport({
        command: calendarServerConfig.command,
        args: calendarServerConfig.args,
        env: {
          ...process.env,
          ...calendarServerConfig.env
        }
      })

      // 创建客户端
      mcpClient = new Client({
        name: 'outlook-calendar-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到服务器
      console.log('🔗 连接到Outlook日历MCP服务器...')
      await mcpClient.connect(mcpTransport)
      console.log('✅ 已连接到Outlook日历MCP服务器')

      // 获取可用工具列表
      const toolsResponse = await mcpClient.listTools()
      availableTools = toolsResponse.tools || []
      console.log('🛠️ Outlook日历MCP可用工具:', availableTools.map(t => t.name))

      // 注册到管理器
      mcpManager.clients.set('outlook-calendar', {
        mcpClient,
        transport: mcpTransport,
        process: null, // 进程由StdioClientTransport管理
        isConnected: true,
        isRealMCP: true,
        configSource: 'mcp-config.json',
        availableTools
      })

      initialized = true
      console.log('✅ Outlook日历MCP服务器初始化完成')

    } catch (sdkError) {
      console.error('❌ MCP SDK加载失败:', sdkError)
      throw sdkError
    }

  } catch (error) {
    console.error('❌ Outlook日历MCP服务器初始化失败:', error)
    throw error
  }
}

function getConnectionStatus() {
  return {
    connected: initialized && mcpClient !== null,
    service: 'outlook-calendar',
    tools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    service: 'outlook-calendar'
  }))
}

async function callTool(toolName, args) {
  if (!initialized || !mcpClient) {
    throw new Error('Outlook日历MCP服务未初始化')
  }

  try {
    console.log(`📅 调用Outlook日历工具: ${toolName}`)
    console.log(`📅 工具参数:`, args)

    const result = await mcpClient.callTool({
      name: toolName,
      arguments: args
    })

    console.log(`📅 工具调用结果:`, result)
    return {
      success: true,
      ...result.content[0]
    }
  } catch (error) {
    console.error(`❌ Outlook日历工具调用失败: ${toolName}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

async function cleanup() {
  console.log('🧹 清理Outlook日历MCP资源...')

  if (mcpClient) {
    try {
      await mcpClient.close()
    } catch (error) {
      console.error('清理Outlook日历MCP客户端失败:', error)
    }
    mcpClient = null
  }

  if (mcpTransport) {
    try {
      await mcpTransport.close()
    } catch (error) {
      console.error('清理Outlook日历MCP传输层失败:', error)
    }
    mcpTransport = null
  }

  availableTools = []
  initialized = false
  console.log('✅ Outlook日历MCP资源清理完成')
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
}