const fs = require('fs')
const path = require('path')
const { getKnowledgeConfig } = require('./config')
const db = require('./db')
const { getKnowledgeEmbedding } = require('./embedding')

/**
 * 后处理 Markdown 内容
 * @param {string} content - Markdown 内容
 * @returns {string} 处理后的内容
 */
function postProcessMarkdown(content) {
  // 清理多余的空行
  content = content.replace(/\n{3,}/g, '\n\n');

  // 修复表格格式
  content = content.replace(/\|\s*\|\s*/g, '| ');
  content = content.replace(/\|\s*$/gm, '|');
  content = content.replace(/^\s*\|/gm, '|');

  // 确保表格前后有空行
  content = content.replace(/([^\n])\n(\|.*\|)/g, '$1\n\n$2');
  content = content.replace(/(\|.*\|)\n([^\n|])/g, '$1\n\n$2');

  // 清理空的表格行
  content = content.replace(/\|\s*\|\s*\|\s*\n/g, '');

  // 为表格添加标题（如果检测到是专家信息表等）
  content = content.replace(/(\|[^|]*姓名[^|]*\|[^|]*部门[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 专家信息表\n\n$1');

  content = content.replace(/(\|[^|]*项目[^|]*\|[^|]*任务[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 项目信息表\n\n$1');

  return content.trim();
}

/**
 * 简单的文档分割
 * @param {string} content - 文档内容
 * @returns {Array<string>} 分割后的片段数组
 */
function splitKnowledgeDocument(content) {
  const KNOWLEDGE_CONFIG = getKnowledgeConfig()
  
  // 首先过滤掉图片内容和其他不相关内容
  const cleanContent = content
    // 移除base64图片
    .replace(/!\[.*?\]\(data:image\/[^)]+\)/g, '')
    // 移除普通图片链接
    .replace(/!\[.*?\]\([^)]+\.(png|jpg|jpeg|gif|webp)[^)]*\)/g, '')
    // 移除HTML图片标签
    .replace(/<img[^>]*>/g, '')
    // 移除多余空行
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim()

  console.log(`📝 文档清理前长度: ${content.length}, 清理后长度: ${cleanContent.length}`)

  const chunks = []
  const paragraphs = cleanContent.split(/\n\s*\n/)

  let currentChunk = ''
  const maxLength = KNOWLEDGE_CONFIG.document.maxSplitLength

  for (const paragraph of paragraphs) {
    // 跳过只包含图片描述或无意义内容的段落
    if (paragraph.trim().length < 10 ||
      paragraph.includes('data:image/') ||
      /^!\[.*?\]/.test(paragraph.trim())) {
      continue
    }

    if ((currentChunk + paragraph).length > maxLength && currentChunk.length > 0) {
      chunks.push(currentChunk.trim())
      currentChunk = paragraph
    } else {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim())
  }

  // 过滤有效内容
  const filteredChunks = chunks.filter(chunk => {
    const trimmed = chunk.trim()
    return trimmed.length > 20 &&
      !trimmed.includes('data:image/') &&
      !/^!\[.*?\]/.test(trimmed)
  })

  console.log(`📝 文档分割结果: ${filteredChunks.length} 个有效片段`)

  // 如果过滤后没有片段，但原文档有内容，则保留整个清理后的文档作为一个片段
  if (filteredChunks.length === 0 && cleanContent.length > 20) {
    console.log('📝 文档内容较短，保留整个清理后的文档作为单个片段')
    return [cleanContent]
  }

  return filteredChunks
}

/**
 * 获取文档内容
 * @param {string} filePath - 文件路径
 * @returns {Promise<string>} 文档内容
 */
async function getKnowledgeDocumentContent(filePath) {
  try {
    const ext = path.extname(filePath).toLowerCase()
    let content = ''

    if (ext === '.txt' || ext === '.md') {
      // 文本文件直接读取
      content = fs.readFileSync(filePath, 'utf-8')
    } else if (ext === '.docx' || ext === '.doc') {
      // Word文档需要转换
      if (!db.mammoth) {
        throw new Error('mammoth 模块未加载，无法处理 Word 文档')
      }

      const result = await db.mammoth.extractRawText({ path: filePath })
      content = result.value

      // 如果有警告，记录但不中断
      if (result.messages.length > 0) {
        console.warn('⚠️ Word文档转换警告:', result.messages)
      }
    } else {
      throw new Error(`不支持的文件格式: ${ext}`)
    }

    // 后处理内容
    if (ext === '.md') {
      content = postProcessMarkdown(content)
    }

    return content
  } catch (error) {
    console.error(`❌ 读取文档内容失败: ${filePath}`, error)
    throw error
  }
}

/**
 * 索引单个文档
 * @param {string} filePath - 文件路径
 * @returns {Promise<Object>} 索引结果
 */
async function indexKnowledgeDocument(filePath) {
  try {
    console.log(`📄 开始索引文档: ${filePath}`)

    // 获取文档内容
    const fileContent = await getKnowledgeDocumentContent(filePath)

    // 分割文档
    const chunks = splitKnowledgeDocument(fileContent)

    if (chunks.length === 0) {
      throw new Error('文档分割后没有有效内容')
    }

    // 获取文件大小
    let fileSize = 0
    try {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath)
        fileSize = stats.size
      }
    } catch (error) {
      console.warn(`⚠️ 无法获取文件大小: ${filePath}`, error.message)
    }

    // 插入文档记录
    const fileName = path.basename(filePath)
    const filePreview = chunks[0].substring(0, 200) + '...'

    const fileResult = await db.libsqlClient.execute({
      sql: `INSERT INTO user_file (file_name, file_path, source_file_path, file_preview, remark, file_size)
            VALUES (?, ?, ?, ?, ?, ?) RETURNING id`,
      args: [fileName, filePath, filePath, filePreview, `通过知识库自动索引于 ${new Date().toLocaleString()}`, fileSize]
    })

    const fileId = fileResult.rows[0].id
    console.log(`✅ 文档记录插入成功，ID: ${fileId}`)

    // 处理每个分割片段
    let successCount = 0
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      try {
        console.log(`📝 处理第 ${i + 1}/${chunks.length} 个片段，长度: ${chunk.length} 字符`)

        // 生成向量
        const embedding = await getKnowledgeEmbedding(chunk)

        // 插入embedding记录
        await db.libsqlClient.execute({
          sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                VALUES (?, ?, ?)`,
          args: [fileId, chunk, embedding]
        })

        successCount++
        console.log(`✅ 片段 ${i + 1} 处理成功`)
      } catch (error) {
        console.error(`❌ 片段 ${i + 1} 处理失败:`, error.message)
        // 继续处理其他片段
      }
    }

    console.log(`✅ 文档索引完成: ${successCount}/${chunks.length} 个片段成功`)
    return {
      success: true,
      fileId,
      totalChunks: chunks.length,
      successChunks: successCount
    }

  } catch (error) {
    console.error(`❌ 文档索引失败: ${filePath}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 清空知识库
 * @returns {Promise<boolean>} 是否成功
 */
async function clearKnowledgeBase() {
  try {
    console.log('🗑️ 开始清空知识库...')

    await db.libsqlClient.batch([
      'DELETE FROM user_file_embd',
      'DELETE FROM user_file'
    ], 'write')

    console.log('✅ 知识库清空完成')
    return true
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return false
  }
}

/**
 * 重建知识库
 * @param {string} dirPath - 文档目录路径
 * @returns {Promise<Object>} 重建结果
 */
async function rebuildKnowledgeBase(dirPath) {
  try {
    console.log(`🔄 开始重建知识库，文档目录: ${dirPath}`)

    // 先清空知识库
    const clearSuccess = await clearKnowledgeBase()
    if (!clearSuccess) {
      throw new Error('清空知识库失败')
    }

    // 获取所有文档文件
    const files = await getKnowledgeDocumentFiles(dirPath)
    console.log(`📁 找到 ${files.length} 个文档文件`)

    let successCount = 0
    let failCount = 0

    for (const file of files) {
      try {
        const result = await indexKnowledgeDocument(file)
        if (result.success) {
          successCount++
        } else {
          failCount++
        }
      } catch (error) {
        console.error(`❌ 索引文件失败: ${file}`, error)
        failCount++
      }
    }

    console.log(`✅ 知识库重建完成: ${successCount} 成功, ${failCount} 失败`)
    return {
      success: true,
      totalFiles: files.length,
      successCount,
      failCount
    }

  } catch (error) {
    console.error('❌ 重建知识库失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取文档目录下的所有支持文件
 * @param {string} dirPath - 目录路径
 * @returns {Promise<Array<string>>} 文件路径数组
 */
async function getKnowledgeDocumentFiles(dirPath) {
  try {
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const supportedFormats = KNOWLEDGE_CONFIG.document.supportedFormats
    const files = []

    function scanDirectory(dir) {
      const items = fs.readdirSync(dir)
      
      for (const item of items) {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          // 递归扫描子目录
          scanDirectory(fullPath)
        } else if (stat.isFile()) {
          const ext = path.extname(item).toLowerCase()
          if (supportedFormats.includes(ext)) {
            files.push(fullPath)
          }
        }
      }
    }

    scanDirectory(dirPath)
    console.log(`📁 扫描目录 ${dirPath}，找到 ${files.length} 个支持的文件`)
    return files

  } catch (error) {
    console.error(`❌ 扫描文档目录失败: ${dirPath}`, error)
    return []
  }
}

module.exports = {
  postProcessMarkdown,
  splitKnowledgeDocument,
  getKnowledgeDocumentContent,
  indexKnowledgeDocument,
  clearKnowledgeBase,
  rebuildKnowledgeBase,
  getKnowledgeDocumentFiles
} 