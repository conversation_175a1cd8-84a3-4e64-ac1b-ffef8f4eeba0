<template>
  <div class="settings-view">
    <div class="view-header">
      <div class="header-content">
        <h1 class="view-title">设置</h1>
      </div>
    </div>

    <div class="settings-container">
      <!-- 设置侧边栏 -->
      <aside class="settings-sidebar">
        <nav class="settings-nav">
          <span
            v-for="item in settingsMenuItems"
            :key="item.id"
            :class="['setting-nav-item', { active: activeSettingTab === item.id }]"
            @click="activeSettingTab = item.id"
          >
            <img :src="activeSettingTab === item.id ? item.iconActive : item.icon" alt="Setting Icon" class="nav-icon">
            <span class="nav-text">{{ item.label }}</span>
          </span>
        </nav>
      </aside>

      <!-- 设置内容 -->
      <main class="settings-content">
        <!-- 基本设置 -->
        <div v-if="activeSettingTab === 'basic'" class="setting-panel">
          <div class="panel-header">
            <h2>基本设置</h2>
            <p>系统启动和开发者选项</p>
          </div>
          
          <div class="setting-group">
            <label class="group-label">启动设置</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">开机自动启动</div>
                  <div class="setting-desc">系统启动时自动启动应用</div>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" v-model="settings.autoStart" />
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">启动时显示主窗口</div>
                  <div class="setting-desc">应用启动时自动显示主界面</div>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" v-model="settings.showOnStartup" />
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 问答模型设置 -->
          <div class="setting-group">
            <label class="group-label">问答模型</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">大模型选择</div>
                  <div class="setting-desc">选择问答使用的大模型</div>
                </div>
                <div class="setting-control">
                  <select v-model="selectedModelId" class="select-input" @change="updateModelSelection">
                    <option v-for="model in availableModels" :key="model.id" :value="model.id">
                      {{ model.name }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- 语音识别设置 -->
          <div class="setting-group">
            <label class="group-label">语音识别</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">语音识别服务</div>
                  <div class="setting-desc">当前使用腾讯云语音识别服务（代理模式）</div>
                </div>
                <div class="setting-control">
                  <div class="status-display">
                    <span class="status-text">腾讯云语音识别</span>
                    <span class="status-badge proxy">代理模式</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件路径设置 -->
          <div class="setting-group">
            <label class="group-label">文件路径设置</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">文件下载和搜索路径</div>
                  <div class="setting-desc">设置文件下载、搜索和打开的默认路径</div>
                </div>
                <div class="setting-control">
                  <div class="file-paths-settings">
                    <div class="custom-paths">
                      <div v-for="(path, index) in filePathsSettings.customPaths" :key="index" class="path-item">
                        <input type="text" :value="path" readonly class="path-input" />
                        <button @click="removePath(index)" class="path-btn danger">删除</button>
                      </div>
                      
                      <div class="path-actions ">
                        <button @click="addCustomPath" class="path-btn success">添加路径</button>
                      </div>
                    </div>
                    
                    <p class="setting-hint">
                      配置MCP文件操作允许访问的路径。默认包含下载目录。
                    </p>
                    
                    <button @click="saveFilePathsSettings" class="save-btn">保存路径设置</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label class="group-label">开发设置</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">启用开发者工具</div>
                  <div class="setting-desc">开启后可以使用F12或Ctrl+Shift+I打开开发者工具</div>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" v-model="settings.enableDevTools" @change="toggleDevTools" />
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 外观设置 -->
        <div v-if="activeSettingTab === 'appearance'" class="setting-panel">
          <div class="panel-header">
            <h2>外观设置</h2>
            <p>主题、颜色和界面定制</p>
          </div>
          
          <div class="setting-group">
            <label class="group-label">主题模式</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">深色模式</div>
                  <div class="setting-desc">切换明亮/黑暗主题</div>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" v-model="themeStore.isDarkMode" />
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="setting-group">
            <label class="group-label">主题颜色</label>
            <div class="color-picker-grid">
              <div
                v-for="color in themeColors"
                :key="color"
                :class="['color-option', { selected: themeStore.themeColor === color }]"
                :style="{ backgroundColor: color }"
                @click="themeStore.setThemeColor(color)"
              ></div>
            </div>
          </div> -->

          <!-- <div class="setting-group">
            <label class="group-label">悬浮窗透明度</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">透明度</div>
                  <div class="setting-desc">调整悬浮窗口的透明度</div>
                </div>
                <div class="setting-control">
                  <div class="range-input-group">
                    <input
                      type="range"
                      v-model="settings.opacity"
                      min="0.3"
                      max="1"
                      step="0.1"
                      class="range-input"
                    />
                    <span class="range-value">{{ Math.round(settings.opacity * 100) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </div>

        <!-- 知识库设置 -->
        <div v-if="activeSettingTab === 'knowledge'" class="setting-panel">
          <div class="panel-header">
            <h2>知识库设置</h2>
            <p>文档索引和搜索配置</p>
          </div>
          
          <div class="setting-group">
            <label class="group-label">文档路径</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">文档目录</div>
                  <div class="setting-desc">选择包含您文档的文件夹，犇犇将自动索引其中的文档内容</div>
                </div>
                <div class="setting-control">
                  <div class="path-input-group">
                    <input
                      type="text"
                      v-model="knowledgeSettings.documentsPath"
                      placeholder="请选择文档目录"
                      class="path-input"
                      readonly
                    />
                    <button @click="selectDocumentsPath" class="path-btn">选择目录</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label class="group-label">搜索配置</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">相似度阈值</div>
                  <div class="setting-desc">设置知识库搜索的最小相似度阈值，值越高搜索结果越精确（当前: {{ (knowledgeSettings.similarityThreshold * 100).toFixed(0) }}%）</div>
                </div>
                <div class="setting-control">
                  <div class="slider-container">
                    <input
                      type="range"
                      v-model.number="knowledgeSettings.similarityThreshold"
                      min="0.1"
                      max="0.9"
                      step="0.05"
                      class="slider"
                      @input="updateKnowledgeSettings"
                    />
                    <div class="slider-value">{{ (knowledgeSettings.similarityThreshold * 100).toFixed(0) }}%</div>
                  </div>
                </div>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">默认返回数量</div>
                  <div class="setting-desc">每次搜索默认返回的文档片段数量</div>
                </div>
                <div class="setting-control">
                  <select v-model.number="knowledgeSettings.defaultLimit" class="select-input" @change="updateKnowledgeSettings">
                    <option value="2">2 个</option>
                    <option value="3">3 个</option>
                    <option value="4">4 个</option>
                    <option value="5">5 个</option>
                    <option value="6">6 个</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label class="group-label">知识库状态</label>
            <div class="status-grid">
              <div class="status-card">
                <div class="status-icon">🗄️</div>
                <div class="status-content">
                  <div class="status-label">数据库状态</div>
                  <div :class="['status-value', knowledgeStatus.isInitialized ? 'success' : 'warning']">
                    {{ knowledgeStatus.isInitialized ? '已初始化' : '未初始化' }}
                  </div>
                </div>
              </div>
              
              <div class="status-card">
                <div class="status-icon">📄</div>
                <div class="status-content">
                  <div class="status-label">已索引文档</div>
                  <div class="status-value">{{ knowledgeStats.totalFiles }} 个文件</div>
                </div>
              </div>
              
              <div class="status-card">
                <div class="status-icon">🧩</div>
                <div class="status-content">
                  <div class="status-label">文档片段</div>
                  <div class="status-value">{{ knowledgeStats.totalSegments }} 个片段</div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label class="group-label">操作</label>
            <div class="action-grid">
              <button
                @click="initializeKnowledge"
                :disabled="knowledgeStatus.isInitializing || knowledgeStatus.isRebuilding"
                class="action-btn primary"
              >
                {{ knowledgeStatus.isInitializing ? '初始化中...' : '初始化知识库' }}
              </button>
              
              <button
                @click="rebuildKnowledge"
                :disabled="knowledgeStatus.isRebuilding || knowledgeStatus.isInitializing"
                class="action-btn warning"
              >
                {{ knowledgeStatus.isRebuilding ? '重建中...' : '重建知识库' }}
              </button>
              
              <button
                @click="indexDocuments"
                :disabled="knowledgeStatus.isIndexing || knowledgeStatus.isRebuilding || !knowledgeSettings.documentsPath"
                class="action-btn success"
              >
                {{ knowledgeStatus.isIndexing ? '索引中...' : '重新索引文档' }}
              </button>
              
              <button
                @click="clearKnowledge"
                :disabled="knowledgeStatus.isClearing || knowledgeStatus.isRebuilding"
                class="action-btn danger"
              >
                {{ knowledgeStatus.isClearing ? '清理中...' : '清空知识库' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 邮件设置 -->
        <div v-if="activeSettingTab === 'email'" class="setting-panel">
          <div class="panel-header">
            <h2>邮件设置</h2>
            <p>邮箱配置和待办事项提取</p>
          </div>
          
          <div class="setting-group">
            <label class="group-label">邮箱配置</label>
            <EmailConfig />
          </div>

          <!-- <div class="setting-group">
            <label class="group-label">邮件服务状态</label>
            <div class="status-grid">
              <div class="status-card">
                <div class="status-icon">📧</div>
                <div class="status-content">
                  <div class="status-label">邮件服务</div>
                  <div :class="['status-value', emailServiceStatus.isInitialized ? 'success' : 'warning']">
                    {{ emailServiceStatus.isInitialized ? '已启用' : '未启用' }}
                  </div>
                </div>
              </div>
              
              <div class="status-card">
                <div class="status-icon">⏱️</div>
                <div class="status-content">
                  <div class="status-label">检查间隔</div>
                  <div class="status-value">5分钟</div>
                </div>
              </div>
            </div>
          </div> -->
        </div>

        <!-- 语音设置 -->
        <div v-if="activeSettingTab === 'voice'" class="setting-panel">
          <div class="panel-header">
            <h2>语音设置</h2>
            <p>语音识别和播报配置</p>
          </div>
          
          <div class="setting-group">
            <label class="group-label">语音识别</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">识别语言</div>
                  <div class="setting-desc">选择语音识别的语言</div>
                </div>
                <div class="setting-control">
                  <select v-model="settings.voiceLanguage" class="select-input">
                    <option value="zh-CN">中文（简体）</option>
                    <option value="zh-TW">中文（繁体）</option>
                    <option value="en-US">英语</option>
                    <option value="ja-JP">日语</option>
                  </select>
                </div>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">识别敏感度</div>
                  <div class="setting-desc">调整语音识别的敏感度</div>
                </div>
                <div class="setting-control">
                  <div class="range-input-group">
                    <input
                      type="range"
                      v-model="settings.voiceSensitivity"
                      min="1"
                      max="10"
                      class="range-input"
                    />
                    <span class="range-value">{{ settings.voiceSensitivity }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label class="group-label">测试</label>
            <div class="setting-items">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">语音识别测试</div>
                  <div class="setting-desc">测试语音识别功能是否正常工作</div>
                </div>
                <div class="setting-control">
                  <button @click="testVoiceRecognition" class="test-btn">
                    {{ isRecording ? '停止录音' : '开始测试' }}
                  </button>
                </div>
              </div>
            </div>
            <div v-if="voiceTestResult" class="test-result">
              识别结果：{{ voiceTestResult }}
            </div>
          </div>
        </div>

        <!-- 关于 -->
        <div v-if="activeSettingTab === 'about'" class="setting-panel">
          <div class="panel-header">
            <h2>关于犇犇</h2>
            <p>版本信息和帮助</p>
          </div>
          
          <div class="about-content">
            <div class="about-card">
              <img :src="logoUrl" alt="Logo" class="about-logo" />
              <div class="about-info">
                <h3 class="app-name">犇犇数字员工助手</h3>
                <div class="version-info">
                  <span class="version-label">版本</span>
                  <span class="version-number">内测v1.0.3</span>
                </div>
              </div>
            </div>
            
            <div class="feature-list">
              <h4>主要功能</h4>
              <ul>
                <li>📚 智能知识库管理</li>
                <li>📋 邮件待办事项提取</li>
                <li>🎤 语音识别交互</li>
                <li>🌙 明暗主题切换</li>
                <li>⚙️ 灵活的配置选项</li>
              </ul>
            </div>

            <!-- 账户操作 -->
            <div class="account-actions">
              <h4>账户操作</h4>
              <div class="action-buttons">
                <button @click="handleLogout" class="logout-btn">
                  <i class="logout-icon">🚪</i>
                  <span>退出登录</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- 状态消息 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useThemeStore } from '../stores/theme.js'
import { useAuthStore } from '../stores/auth.js'
import EmailConfig from '../components/EmailConfig.vue'
import logoUrl from '/assets/logo.png?url'
import { 
  initKnowledgeDatabase, 
  indexDocument, 
  searchKnowledge, 
  getKnowledgeStats, 
  clearKnowledgeBase, 
  rebuildKnowledgeBase 
} from '../utils/knowledge/knowledgeClient.js'
import { 
  getDocumentsPath, 
  setDocumentsPath, 
  getDefaultDocumentsPath 
} from '../utils/knowledge/config.js'

// 导入ASR提供商管理工具
import asrProviderManager from '../utils/asrProviderManager.js'
// 导入模型管理工具
import modelManager from '../utils/modelManager.js'

// 导入设置页面图标资源
import icon1Img from '/assets/icon1-1.png'
import icon1OnImg from '/assets/icon1-1-on.png'
import icon2Img from '/assets/icon2-1.png'
import icon2OnImg from '/assets/icon2-1-on.png'
import icon3Img from '/assets/icon3-1.png'
import icon3OnImg from '/assets/icon3-1-on.png'
import icon4Img from '/assets/icon4-1.png'
import icon4OnImg from '/assets/icon4-1-on.png'

export default {
  name: 'SettingsView',
  components: {
    EmailConfig
  },
  setup() {
    const themeStore = useThemeStore()
    const authStore = useAuthStore()
    const activeSettingTab = ref('basic')
    const isRecording = ref(false)
    const voiceTestResult = ref('')
    const statusMessage = ref('')
    const statusType = ref('info')

    const settingsMenuItems = [
      { 
        id: 'basic', 
        label: '基本设置', 
        icon: icon1Img,
        iconActive: icon1OnImg
      },
      { 
        id: 'appearance', 
        label: '外观设置', 
        icon: icon2Img,
        iconActive: icon2OnImg
      },
      // { id: 'knowledge', label: '知识库', icon: 'icon-knowledge' },
      { 
        id: 'email', 
        label: '邮件设置', 
        icon: icon3Img,
        iconActive: icon3OnImg
      },
      // { id: 'voice', label: '语音设置', icon: 'icon-voice' },
      { 
        id: 'about', 
        label: '关于', 
        icon: icon4Img,
        iconActive: icon4OnImg
      }
    ]

    const themeColors = [
      '#667eea', '#764ba2', '#f093fb', '#f5576c',
      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
    ]

    const settings = reactive({
      autoStart: true,
      showOnStartup: false,
      enableDevTools: true,
      voiceLanguage: 'zh-CN',
      voiceSensitivity: 5,
      opacity: 0.9
    })

    // 文件路径设置
    const filePathsSettings = reactive({
      downloadsDir: '',
      customPaths: []
    })

    // 知识库相关状态
    const knowledgeSettings = reactive({
      documentsPath: '',
      autoIndex: true,
      searchLimit: 3,
      similarityThreshold: 0.5,
      defaultLimit: 4
    })

    const knowledgeStatus = reactive({
      isInitialized: false,
      isInitializing: false,
      isIndexing: false,
      isClearing: false,
      isRebuilding: false
    })

    const knowledgeStats = reactive({
      totalFiles: 0,
      totalSegments: 0
    })

    const emailServiceStatus = reactive({
      isInitialized: false
    })

    // ASR提供商相关 - 已禁用阿里云，只使用腾讯云
    const asrProvider = ref('tencent')
    const asrProviders = ref([{ id: 'tencent', name: '腾讯云语音识别' }])
    
    // 问答模型相关
    const selectedModelId = ref('')
    const availableModels = ref([])

    // 显示状态消息
    const showStatus = (message, type = 'info') => {
      statusMessage.value = message
      statusType.value = type
      setTimeout(() => {
        statusMessage.value = ''
      }, 3000)
    }

    // 退出登录
    const handleLogout = async () => {
      try {
        console.log('🚪 开始退出登录')
        const result = await authStore.logout()
        if (result.success) {
          showStatus('退出登录成功', 'success')
        } else {
          showStatus('退出登录失败: ' + result.message, 'error')
        }
      } catch (error) {
        console.error('🚪 退出登录失败:', error)
        showStatus('退出登录失败: ' + error.message, 'error')
      }
    }

    // 切换开发者工具
    const toggleDevTools = async () => {
      try {
        console.log('切换开发者工具状态:', settings.enableDevTools)
        
        if (window.electronAPI && window.electronAPI.toggleDevTools) {
          await window.electronAPI.toggleDevTools(settings.enableDevTools)
          showStatus(
            settings.enableDevTools ? '开发者工具已启用' : '开发者工具已禁用',
            'success'
          )
        } else {
          throw new Error('开发者工具API不可用')
        }
      } catch (error) {
        console.error('切换开发者工具失败:', error)
        // 回滚状态
        settings.enableDevTools = !settings.enableDevTools
        showStatus('切换开发者工具失败: ' + error.message, 'error')
      }
    }

    // 语音识别测试
    const testVoiceRecognition = () => {
      if (isRecording.value) {
        // 停止录音
        isRecording.value = false
        voiceTestResult.value = '测试完成：您好，这是语音识别测试'
        showStatus('语音识别测试完成', 'success')
      } else {
        // 开始录音
        isRecording.value = true
        voiceTestResult.value = ''
        showStatus('开始语音识别测试...', 'info')
        
        // 模拟录音过程
        setTimeout(() => {
          if (isRecording.value) {
            isRecording.value = false
            voiceTestResult.value = '识别成功：您好，犇犇助手'
            showStatus('语音识别测试成功', 'success')
          }
        }, 3000)
      }
    }

    // 知识库操作函数
    const initializeKnowledge = async () => {
      if (knowledgeStatus.isInitializing) return
      
      knowledgeStatus.isInitializing = true
      try {
        await initKnowledgeDatabase()
        await refreshKnowledgeStats()
        showStatus('知识库初始化成功', 'success')
        knowledgeStatus.isInitialized = true
      } catch (error) {
        console.error('知识库初始化失败:', error)
        showStatus('知识库初始化失败: ' + error.message, 'error')
      } finally {
        knowledgeStatus.isInitializing = false
      }
    }

    const rebuildKnowledge = async () => {
      if (knowledgeStatus.isRebuilding) return
      
      if (!confirm('重建知识库将删除所有现有数据，确定继续吗？')) return
      
      knowledgeStatus.isRebuilding = true
      try {
        await rebuildKnowledgeBase()
        await refreshKnowledgeStats()
        showStatus('知识库重建成功', 'success')
        knowledgeStatus.isInitialized = true
      } catch (error) {
        console.error('知识库重建失败:', error)
        showStatus('知识库重建失败: ' + error.message, 'error')
      } finally {
        knowledgeStatus.isRebuilding = false
      }
    }

    const selectDocumentsPath = async () => {
      try {
        const result = await window.electronAPI.selectDirectory()
        if (result && result.filePaths && result.filePaths.length > 0) {
          knowledgeSettings.documentsPath = result.filePaths[0]
          await setDocumentsPath(knowledgeSettings.documentsPath)
          showStatus('文档路径设置成功', 'success')
        }
      } catch (error) {
        console.error('选择文档路径失败:', error)
        showStatus('选择文档路径失败: ' + error.message, 'error')
      }
    }

    const indexDocuments = async () => {
      if (knowledgeStatus.isIndexing || !knowledgeSettings.documentsPath) return
      
      knowledgeStatus.isIndexing = true
      try {
        const files = await window.electronAPI.getDirectoryFiles(
          knowledgeSettings.documentsPath,
          ['.txt', '.md', '.docx', '.doc', '.pdf']
        )
        
        for (const file of files) {
          await indexDocument(file)
        }
        
        await refreshKnowledgeStats()
        showStatus('文档索引成功', 'success')
      } catch (error) {
        console.error('文档索引失败:', error)
        showStatus('文档索引失败: ' + error.message, 'error')
      } finally {
        knowledgeStatus.isIndexing = false
      }
    }

    const clearKnowledge = async () => {
      if (knowledgeStatus.isClearing) return
      
      if (!confirm('确定要清空知识库吗？此操作不可撤销。')) return
      
      knowledgeStatus.isClearing = true
      try {
        await clearKnowledgeBase()
        await refreshKnowledgeStats()
        showStatus('知识库已清空', 'success')
      } catch (error) {
        console.error('清空知识库失败:', error)
        showStatus('清空知识库失败: ' + error.message, 'error')
      } finally {
        knowledgeStatus.isClearing = false
      }
    }

    const refreshKnowledgeStats = async () => {
      try {
        const stats = await getKnowledgeStats()
        if (stats && typeof stats === 'object') {
          knowledgeStats.totalFiles = stats.totalFiles || 0
          knowledgeStats.totalSegments = stats.totalSegments || 0
        }
      } catch (error) {
        console.error('获取知识库统计失败:', error)
      }
    }

    const loadKnowledgeSettings = async () => {
      try {
        const path = await getDocumentsPath()
        if (path) {
          knowledgeSettings.documentsPath = path
        } else {
          knowledgeSettings.documentsPath = await getDefaultDocumentsPath()
        }
        
        // 加载知识库配置
        const { getKnowledgeConfig, updateKnowledgeConfig } = await import('../utils/config/modelConfig.js')
        const config = getKnowledgeConfig()
        knowledgeSettings.similarityThreshold = config.search.similarityThreshold || 0.5
        knowledgeSettings.defaultLimit = config.search.defaultLimit || 4
        
        await refreshKnowledgeStats()
        // 检查是否已初始化
        if (knowledgeStats.totalFiles > 0 || knowledgeStats.totalSegments > 0) {
          knowledgeStatus.isInitialized = true
        }
      } catch (error) {
        console.error('加载知识库设置失败:', error)
      }
    }

    const updateKnowledgeSettings = async () => {
      try {
        const { updateKnowledgeConfig } = await import('../utils/config/modelConfig.js')
        await updateKnowledgeConfig({
          search: {
            similarityThreshold: knowledgeSettings.similarityThreshold,
            defaultLimit: knowledgeSettings.defaultLimit
          }
        })
        
        // 通知主进程配置变更
        if (window.electronAPI && window.electronAPI.notifyEvent) {
          window.electronAPI.notifyEvent('knowledge-config-changed', {
            similarityThreshold: knowledgeSettings.similarityThreshold,
            defaultLimit: knowledgeSettings.defaultLimit
          })
        }
        
        showStatus('知识库设置已更新', 'success')
      } catch (error) {
        console.error('更新知识库设置失败:', error)
        showStatus('更新知识库设置失败: ' + error.message, 'error')
      }
    }

    // ASR提供商管理 - 已禁用阿里云，只使用腾讯云
    const updateASRProvider = () => {
      // 强制使用腾讯云
      asrProvider.value = 'tencent'
      asrProviderManager.setProvider('tencent')
      showStatus('当前使用腾讯云语音识别服务（代理模式）', 'info')
    }
    
    // 问答模型管理
    const updateModelSelection = () => {
      modelManager.setModel(selectedModelId.value)
      const modelName = modelManager.getModelName()
      showStatus(`问答模型已切换为: ${modelName}`, 'success')
      
      // 通知主进程模型变更
      try {
        if (window.electronAPI && window.electronAPI.notifyEvent) {
          window.electronAPI.notifyEvent('model-changed', {
            modelId: selectedModelId.value,
            modelName: modelName
          })
        }
        
        // 通知浮动窗口(如果存在)
        if (window.floatingWindow && window.floatingWindow.postMessage) {
          window.floatingWindow.postMessage({
            type: 'model-changed',
            modelId: selectedModelId.value,
            modelName: modelName
          }, '*')
        }
      } catch (error) {
        console.error('通知模型变更失败:', error)
      }
    }

    // 文件路径设置相关函数
    const loadFilePathsSettings = async () => {
      try {
        if (window.electronAPI && window.electronAPI.getFilePathsConfig) {
          const config = await window.electronAPI.getFilePathsConfig()
          filePathsSettings.downloadsDir = config.downloadsDir || 'downloads'
          filePathsSettings.customPaths = Array.isArray(config.customPaths) ? [...config.customPaths] : []
          
          console.log('📂 已加载文件路径配置:', filePathsSettings)
        } else {
          console.log('📂 electronAPI不可用，使用默认配置')
          filePathsSettings.downloadsDir = 'downloads'
          filePathsSettings.customPaths = []
        }
      } catch (error) {
        console.error('📂 加载文件路径配置失败:', error)
        // 如果加载失败，使用默认配置
        filePathsSettings.downloadsDir = 'downloads'
        filePathsSettings.customPaths = []
      }
    }
    
    const addCustomPath = async () => {
      try {
        if (window.electronAPI && window.electronAPI.selectCustomPath) {
          const result = await window.electronAPI.selectCustomPath()
          if (!result.canceled && result.path) {
            // 检查路径是否已存在
            if (!filePathsSettings.customPaths.includes(result.path)) {
              filePathsSettings.customPaths.push(result.path)
              console.log('📂 已添加路径:', result.path)
              showStatus('路径添加成功', 'success')
            } else {
              showStatus('该路径已经添加过了', 'warning')
            }
          }
        } else {
          // 开发环境fallback
          const path = prompt('请输入自定义路径:')
          if (path && !filePathsSettings.customPaths.includes(path)) {
            filePathsSettings.customPaths.push(path)
            showStatus('路径添加成功', 'success')
          }
        }
      } catch (error) {
        console.error('📂 添加自定义路径失败:', error)
        // 如果IPC调用失败，使用fallback
        const path = prompt('请输入自定义路径:')
        if (path && !filePathsSettings.customPaths.includes(path)) {
          filePathsSettings.customPaths.push(path)
          showStatus('路径添加成功', 'success')
        }
      }
    }
    
    const removePath = (index) => {
      if (index >= 0 && index < filePathsSettings.customPaths.length) {
        filePathsSettings.customPaths.splice(index, 1)
        console.log('📂 已移除路径，索引:', index)
        showStatus('路径已移除', 'success')
      }
    }
    
    const saveFilePathsSettings = async () => {
      try {
        if (window.electronAPI && window.electronAPI.updateFilePathsConfig) {
          // 保存配置
          const result = await window.electronAPI.updateFilePathsConfig({
            downloadsDir: filePathsSettings.downloadsDir,
            allowCustomPaths: true, // 默认允许自定义路径
            customPaths: [...filePathsSettings.customPaths]
          })
          
          if (result.success) {
            showStatus('文件路径配置已保存', 'success')
          } else {
            showStatus('保存配置失败: ' + (result.error || '未知错误'), 'error')
          }
        } else {
          console.log('📂 保存文件路径配置(开发模式):', filePathsSettings)
          showStatus('文件路径配置已保存(开发模式)', 'success')
        }
      } catch (error) {
        console.error('📂 保存文件路径配置失败:', error)
        showStatus('保存配置失败: ' + error.message, 'error')
      }
    }

    onMounted(() => {
      console.log('设置页面已挂载')
      loadKnowledgeSettings()
      // 加载文件路径配置
      loadFilePathsSettings()
      // 初始化ASR提供商数据 - 强制使用腾讯云
      asrProviders.value = [{ id: 'tencent', name: '腾讯云语音识别' }]
      asrProvider.value = 'tencent'
      // 初始化问答模型数据
      availableModels.value = modelManager.getModelList()
      selectedModelId.value = modelManager.getModelId()
    })

    return {
      themeStore,
      authStore,
      activeSettingTab,
      settingsMenuItems,
      themeColors,
      settings,
      // 文件路径设置
      filePathsSettings,
      addCustomPath,
      removePath,
      saveFilePathsSettings,
      knowledgeSettings,
      knowledgeStatus,
      knowledgeStats,
      emailServiceStatus,
      isRecording,
      voiceTestResult,
      statusMessage,
      statusType,
      showStatus,
      handleLogout,
      toggleDevTools,
      testVoiceRecognition,
      initializeKnowledge,
      rebuildKnowledge,
      selectDocumentsPath,
      indexDocuments,
      clearKnowledge,
      logoUrl,
      // ASR相关
      asrProvider,
      asrProviders,
      updateASRProvider,
      // 问答模型相关
      selectedModelId,
      availableModels,
      updateModelSelection
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.view-header {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  .view-title {
    margin: 8px 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
  }
}

.settings-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  border-radius: 24px 0 0 0;
  background: var(--view-content-bg);
}

.settings-sidebar {
  width: 240px;
  padding: 24px 0;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
  border-radius: 0 8px 8px 0;
  margin-right: 8px;
  font-weight: bold;
  .nav-icon {
    width: 22px;
    height: 22px;
  }

  &:hover {
    color: var(--primary-color);
  }

  &.active {
    color: var(--primary-color);
  }
}

.settings-content {
  flex: 1;
  overflow: auto;
  padding: 32px;
  background: var(--bg-secondary);
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;
    
    &:hover {
      background: var(--text-secondary);
    }
  }
}

.setting-panel {
  max-width: 800px;
}

.panel-header {
  margin-bottom: 32px;
  
  h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-dark);
  }
  
  p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 14px;
  }
}

.setting-group {
  margin-bottom: 32px;
  
  .group-label {
    display: block;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.setting-items {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--shadow-modal);
  }
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-light);
  transition: background 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: var(--card-bg-hover);
  }
}

.setting-info {
  flex: 1;
  
  .setting-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 4px;
  }
  
  .setting-desc {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
  }
}

.setting-control {
  flex-shrink: 0;
  margin-left: 20px;
}

// 开关样式
.switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
    
    &:checked + .slider {
      background-color: var(--primary-color);
      
      &:before {
        transform: translateX(20px);
      }
    }
  }
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color-dark);
  transition: 0.3s;
  border-radius: 12px;
  
  &:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: var(--text-inverse);
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

// 选择器样式
.select-input {
  padding: 8px 12px;
  border: 1px solid var(--border-color-dark);
  border-radius: 6px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  color: var(--text-dark);
  font-size: 14px;
  cursor: pointer;
  min-width: 150px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  }
  
  &:hover {
    border-color: var(--primary-color);
  }
}

// 状态显示样式
.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--border-color-dark);
  border-radius: 6px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  min-height: 38px;
  min-width: 150px;
}

.status-text {
  font-size: 14px;
  color: var(--text-dark);
  font-weight: 500;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.proxy {
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
  }
}

// 范围输入样式
.range-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-input {
  width: 120px;
  height: 4px;
  border-radius: 2px;
  background: var(--border-color-dark);
  outline: none;
  -webkit-appearance: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  &::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

.range-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-dark);
  min-width: 40px;
  padding: 4px 8px;
  background: var(--badge-info-bg);
  color: var(--badge-info-text);
  border-radius: 4px;
  font-size: 12px;
}

// 颜色选择器
.color-picker-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 12px;
  max-width: 400px;
  padding: 16px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 3px solid transparent;
  position: relative;
  
  &:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-card);
  }
  
  &.selected {
    border-color: var(--text-dark);
    transform: scale(1.1);
    box-shadow: var(--shadow-modal);
    
    &::after {
      content: '✓';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--text-inverse);
      font-weight: bold;
      font-size: 14px;
    }
  }
}

// 路径输入组
.path-input-group {
  display: flex;
  gap: 8px;
  width: 100%;
  max-width: 400px;
}

.path-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color-dark);
  border-radius: 6px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  color: var(--text-dark);
  font-size: 14px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  }
}

.path-btn {
  padding: 8px 16px;
  border: 1px solid var(--btn-primary-bg);
  border-radius: 6px;
  background: var(--btn-primary-bg);
  color: var(--text-inverse);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }
}

// 状态网格
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-card {
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  padding: 16px;
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--card-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modal);
  }
}

.status-icon {
  font-size: 24px;
  color: var(--text-dark);
}

.status-content {
  .status-label {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
  }
  
  .status-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-dark);
    
    &.success {
      color: var(--success-color);
    }
    
    &.warning {
      color: var(--warning-color);
    }
    
    &.error {
      color: var(--btn-danger-bg);
    }
  }
}

// 操作网格
.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.action-btn {
  padding: 12px 20px;
  border: 1px solid var(--border-color-dark);
  border-radius: 8px;
  background: var(--btn-secondary-bg);
  backdrop-filter: blur(10px);
  color: var(--text-dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: var(--btn-secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.primary {
    background: var(--btn-primary-bg);
    color: var(--text-inverse);
    border-color: var(--btn-primary-bg);

    &:hover:not(:disabled) {
      background: var(--btn-primary-hover);
      border-color: var(--btn-primary-hover);
    }
  }
  
  &.success {
    background: var(--success-color);
    color: var(--text-inverse);
    border-color: var(--success-color);

    &:hover:not(:disabled) {
      background: #1e7e34;
      border-color: #1e7e34;
    }
  }
  
  &.warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    border-color: var(--warning-color);

    &:hover:not(:disabled) {
      background: #e0a800;
      border-color: #e0a800;
    }
  }
  
  &.danger {
    background: var(--btn-danger-bg);
    color: var(--text-inverse);
    border-color: var(--btn-danger-bg);

    &:hover:not(:disabled) {
      background: var(--btn-danger-hover);
      border-color: var(--btn-danger-hover);
    }
  }
}

.test-btn {
  padding: 8px 16px;
  border: 1px solid var(--btn-primary-bg);
  border-radius: 6px;
  background: var(--btn-primary-bg);
  color: var(--text-inverse);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--btn-primary-hover);
    border-color: var(--btn-primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }
}

.test-result {
  margin-top: 12px;
  padding: 12px;
  background: var(--card-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--card-border);
  border-radius: 6px;
  font-size: 14px;
  color: var(--text-dark);
  box-shadow: var(--card-shadow);
}

// 关于页面
.about-content {
  .about-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 24px;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;

    &:hover {
      background: var(--card-bg-hover);
      transform: translateY(-2px);
      box-shadow: var(--shadow-modal);
    }
  }
  
  .about-logo {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: var(--shadow-card);
  }
  
  .about-info {
    .app-name {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: var(--text-dark);
    }
    
    .app-description {
      margin: 0 0 12px 0;
      color: var(--text-secondary);
      font-size: 16px;
    }
    
    .version-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .version-label {
        color: var(--text-secondary);
        font-size: 14px;
      }
      
      .version-number {
        background: var(--badge-info-bg);
        color: var(--badge-info-text);
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
  
  .feature-list {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 12px;
    padding: 24px;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;

    &:hover {
      box-shadow: var(--shadow-modal);
    }
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-dark);
    }
    
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      
      li {
        padding: 8px 0;
        color: var(--text-secondary);
        font-size: 14px;
        transition: color 0.2s ease;
        
        &:hover {
          color: var(--text-dark);
        }
        
        &:not(:last-child) {
          border-bottom: 1px solid var(--border-color-light);
        }
      }
    }
  }

  .account-actions {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 12px;
    padding: 24px;
    backdrop-filter: blur(10px);
    box-shadow: var(--card-shadow);
    transition: all 0.2s ease;
    margin-top: 24px;

    &:hover {
      box-shadow: var(--shadow-modal);
    }
    
    h4 {
      margin: 0 0 16px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--text-dark);
    }
    
    .action-buttons {
      display: flex;
      gap: 12px;
    }
    
    .logout-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        background: #c0392b;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
      
      .logout-icon {
        font-size: 16px;
      }
    }
  }
}

.status-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: var(--shadow-modal);
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: statusSlideIn 0.3s ease-out;

  &.success {
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
    border: 1px solid var(--badge-success-text);
  }

  &.error {
    background: var(--badge-error-bg);
    color: var(--badge-error-text);
    border: 1px solid var(--badge-error-text);
  }

  &.info {
    background: var(--badge-info-bg);
    color: var(--badge-info-text);
    border: 1px solid var(--badge-info-text);
  }
}

@keyframes statusSlideIn {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 暗黑主题下的特殊处理
.dark-theme .settings-view {
  .status-icon {
    filter: brightness(1.2);
  }

  .about-logo {
    filter: brightness(0.9);
  }

  .color-option.selected::after {
    color: var(--text-dark);
  }
}

// 选择文本样式
.settings-view {
  ::selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }

  ::-moz-selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }
}

// 焦点样式增强
.setting-nav-item:focus,
.action-btn:focus,
.test-btn:focus,
.path-btn:focus,
.select-input:focus,
.switch input:focus + .slider {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// 图标字体样式已移除，现在使用图片图标

// 滑块样式
.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  
  .slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--border-color);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      appearance: none;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
      }
    }
    
    &::-moz-range-thumb {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: var(--primary-color);
      cursor: pointer;
      border: none;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      transition: all 0.2s ease;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
      }
    }
    
    &:focus {
      outline: none;
      
      &::-webkit-slider-thumb {
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.3);
      }
      
      &::-moz-range-thumb {
        box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.3);
      }
    }
  }
  
  .slider-value {
    min-width: 50px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
    background: var(--badge-primary-bg);
    padding: 4px 8px;
    border-radius: 4px;
  }
}

// 文件路径设置相关样式
.file-paths-settings {
  margin-top: 10px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  
  input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }
  
  span {
    font-size: 14px;
    color: var(--text-secondary);
  }
}

.custom-paths {
  margin-top: 12px;
  padding: 12px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
}

.path-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .path-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: var(--input-bg);
    color: var(--text-secondary);
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
    }
  }
}

.path-actions {
  margin-top: 12px;
  display: flex;
  flex-direction: column;
}

.path-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &.success {
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
    
    &:hover {
      background: var(--badge-success-text);
      color: white;
    }
  }
  
  &.danger {
    background: var(--badge-error-bg);
    color: var(--badge-error-text);
    
    &:hover {
      background: var(--badge-error-text);
      color: white;
    }
  }
}

.save-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  background: var(--primary-color);
  color: white;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  margin-top: 12px;
  width: 100%;
  &:hover {
    transform: translateY(-1px);
  }
}

.setting-hint {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 8px;
  line-height: 1.4;
}
</style> 