/**
 * 版本比较工具函数
 */

/**
 * 检查是否需要更新
 * 直接比较版本字符串是否一致，不用管小数点格式
 * @param {string} currentVersion 当前版本
 * @param {string} latestVersion 最新版本
 * @returns {boolean} 是否需要更新
 */
export function needsUpdate(currentVersion, latestVersion) {
  if (!currentVersion || !latestVersion) {
    return false
  }
  
  // 直接字符串比较，不一致就认为需要更新
  const current = currentVersion.trim()
  const latest = latestVersion.trim()
  
  console.log('🔄 版本比较:', {
    current,
    latest,
    needsUpdate: current !== latest
  })
  
  return current !== latest
}

/**
 * 获取当前应用版本号
 * @returns {Promise<string>} 当前版本号
 */
export async function getCurrentVersion() {
  // 从 package.json 获取版本号
  try {
    // 在渲染进程中，可以通过 electron API 获取
    if (window.electronAPI && window.electronAPI.getAppVersion) {
      return await window.electronAPI.getAppVersion()
    }
    
    // fallback: 从全局变量获取（如果主进程设置了的话）
    if (window.APP_VERSION) {
      return window.APP_VERSION
    }
    
    // 最终 fallback
    return '1.0.0-beta'
  } catch (error) {
    console.error('🔄 获取应用版本失败:', error)
    return '1.0.0-beta'
  }
}

/**
 * 格式化版本号显示
 * @param {string} version 版本号
 * @returns {string} 格式化后的版本号
 */
export function formatVersion(version) {
  return version || '未知版本'
} 