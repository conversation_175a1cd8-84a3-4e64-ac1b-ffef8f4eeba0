# main.js 拆分方案

> 本文档用于记录和跟踪 `src/main/main.js` 的拆分重构过程。每完成一步，请在对应步骤后打勾（✅），并在下一步前由人工确认。

---

## 总体目标
- 将 main.js 拆分为多个职责单一、结构清晰的模块文件，便于维护和扩展。
- 每一步拆分后，保证主进程功能不变，逐步迁移。
- 每一步拆分完成后，需人工确认再进行下一步。

---

## 拆分步骤

### 步骤 1：基础结构与入口文件
- [ ] 新建 `src/main/` 下的 `bootstrap.js` 或 `index.js`，作为主进程入口。
- [ ] 仅保留 Electron 启动、窗口创建、主流程初始化等最小启动逻辑。
- [ ] 其余所有业务逻辑、服务类、工具函数全部移出。
- [ ] main.js 只负责 require/导入各子模块并启动。

### 步骤 2：API 客户端与主进程 API 封装 ✅
- [x] 拆分 `createMainApiClient`、`callMainAIService`、`handleMainApiError` 等 API 相关函数到 `src/main/apiClient.js`。
- [x] main.js 只通过 require 使用。

### 步骤 3：知识库相关逻辑 ✅
- [x] 拆分所有知识库相关函数（如 `searchKnowledge`、`indexKnowledgeDocument`、`getKnowledgeEmbedding` 等）到 `src/main/knowledge/` 目录下，建议按功能拆分为 `service.js`、`db.js`、`embedding.js` 等。
- [x] main.js 只通过 require 使用。

### 步骤 4：MCP 客户端与服务管理 ✅
- [x] 拆分 MCPClientManager 相关类和方法到 `src/main/mcp/` 目录下，建议分为 `clientManager.js`、`filesystem.js`、`email.js`、`word.js`、`weather.js`、`browser.js`、`calendar.js` 等。
- [x] main.js 只通过 require 使用。

### 步骤 5：邮件服务与日历服务
- [x] 拆分 `EmailService`、`OutlookCalendarService` 到 `src/main/services/` 目录下。
- [x] main.js 只通过 require 使用。

### 步骤 6：窗口与托盘管理 ✅
- [x] 拆分窗口创建、托盘、缩放、悬浮窗等相关逻辑到 `src/main/window/` 目录下。
- [x] main.js 只通过 require 使用。

### 步骤 7：IPC 事件注册与分发 ✅
- [x] 拆分所有 `ipcMain.handle/on` 相关注册逻辑到 `src/main/ipc/` 目录下，按业务分文件。
- [x] main.js 只通过 require 使用。

### 步骤 8：配置与工具函数 ✅
- [x] 拆分配置、工具函数、全局变量等到 `src/main/utils/`、`src/main/config/` 目录下。

---

## 目录结构建议

```
src/main/
  ├── index.js (主入口)
  ├── apiClient.js
  ├── knowledge/
  │     ├── service.js
  │     ├── db.js
  │     └── embedding.js
  ├── mcp/
  │     ├── clientManager.js
  │     ├── filesystem.js
  │     ├── email.js
  │     ├── word.js
  │     ├── weather.js
  │     ├── browser.js
  │     └── calendar.js
  ├── services/
  │     ├── emailService.js
  │     └── outlookCalendarService.js
  ├── window/
  │     ├── mainWindow.js
  │     ├── floatingWindow.js
  │     └── tray.js
  ├── ipc/
  │     ├── knowledge.js
  │     ├── mcp.js
  │     ├── email.js
  │     ├── calendar.js
  │     └── ...
  ├── utils/
  └── config/
```

---

## 拆分进度记录

- [✅] 步骤 1：基础结构与入口文件
- [✅] 步骤 2：API 客户端与主进程 API 封装
- [✅] 步骤 3：知识库相关逻辑
- [✅] 步骤 4：MCP 客户端与服务管理
- [✅] 步骤 5：邮件服务与日历服务
- [✅] 步骤 6：窗口与托盘管理
- [✅] 步骤 7：IPC 事件注册与分发
- [✅] 步骤 8：配置与工具函数

---

> 每完成一步，请在本文件对应步骤后打勾（✅），并由人工确认后再进行下一步。 